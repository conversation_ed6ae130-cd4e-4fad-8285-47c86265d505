<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气泡图双击功能最终测试</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-container {
            position: relative;
            height: 400px;
        }
        .instructions {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .instructions h3 {
            margin-top: 0;
            color: white;
        }
        .test-status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 8px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #333; margin-bottom: 30px;">🎯 气泡图双击功能测试</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <ul>
                <li><strong>双击人员气泡</strong> → 弹出该人员的产品气泡图</li>
                <li><strong>双击产品气泡</strong> → 弹出该产品在各医院的占比气泡图</li>
                <li><strong>在弹窗中双击医院气泡</strong> → 弹出该医院的产品气泡图</li>
                <li><strong>点击"查看80%销量医院"</strong> → 显示医院列表</li>
            </ul>
        </div>

        <div class="test-status">
            <strong>✅ 状态：</strong> 已修复 getCurrentFilters 函数问题，现在使用 getSelectedFilters
        </div>

        <div class="chart-section">
            <div class="chart-title">👥 人员气泡图 - 双击测试</div>
            <div class="chart-container">
                <canvas id="personnelChart"></canvas>
            </div>
        </div>

        <div class="chart-section">
            <div class="chart-title">📦 产品气泡图 - 双击测试</div>
            <div class="chart-container">
                <canvas id="productChart"></canvas>
            </div>
        </div>

        <div class="test-log" id="testLog">
            <strong>📝 测试日志：</strong><br>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        // 模拟筛选器函数
        window.getSelectedFilters = function() {
            log('📊 调用 getSelectedFilters 函数');
            return {
                months: ['2024-01'],
                products: [],
                personnel: [],
                terminals: []
            };
        };

        // 模拟多选筛选器函数
        window.getMultiSelectValues = function(type) {
            log(`🔍 调用 getMultiSelectValues(${type})`);
            return [];
        };

        // 测试数据
        const personnelTestData = [
            { x: 85, y: 15, r: 25, label: '张三', sales: 120000, achievement: 85, growth: 15 },
            { x: 92, y: -5, r: 30, label: '李四', sales: 150000, achievement: 92, growth: -5 },
            { x: 78, y: 25, r: 20, label: '王五', sales: 98000, achievement: 78, growth: 25 }
        ];

        const productTestData = [
            { x: 88, y: 12, r: 28, label: '产品A', sales: 200000, achievement: 88, growth: 12 },
            { x: 95, y: -3, r: 32, label: '产品B', sales: 250000, achievement: 95, growth: -3 },
            { x: 72, y: 18, r: 22, label: '产品C', sales: 160000, achievement: 72, growth: 18 }
        ];

        // 创建图表函数
        function createChart(canvasId, data, title, type) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            const chart = new Chart(ctx, {
                type: 'bubble',
                data: {
                    datasets: [{
                        label: title,
                        data: data,
                        backgroundColor: data.map((_, i) => `hsla(${i * 120}, 70%, 60%, 0.7)`),
                        borderColor: data.map((_, i) => `hsla(${i * 120}, 70%, 50%, 1)`),
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${title} - 双击气泡进行测试`
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return context[0].raw.label;
                                },
                                label: function(context) {
                                    const data = context.raw;
                                    return [
                                        `销售金额: ¥${(data.sales / 10000).toFixed(1)}万`,
                                        `达成率: ${data.achievement}%`,
                                        `增长率: ${data.growth >= 0 ? '+' : ''}${data.growth}%`
                                    ];
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '达成率 (%)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '增长率 (%)'
                            }
                        }
                    }
                }
            });

            // 添加双击事件
            canvas.addEventListener('dblclick', function(event) {
                log(`🖱️ 检测到双击事件 - ${type}图表`);
                
                const points = chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);
                log(`🎯 找到的点数量: ${points.length}`);
                
                if (points.length > 0) {
                    const point = points[0];
                    const dataIndex = point.index;
                    const bubbleData = chart.data.datasets[0].data[dataIndex];
                    
                    log(`✨ 双击的${type}: ${bubbleData.label}`);
                    
                    // 根据类型调用不同的弹窗函数
                    if (type === '人员') {
                        if (typeof showPersonnelProductBubbleModal === 'function') {
                            log(`🚀 调用 showPersonnelProductBubbleModal(${bubbleData.label})`);
                            showPersonnelProductBubbleModal(bubbleData.label);
                        } else {
                            log('❌ showPersonnelProductBubbleModal 函数不存在');
                        }
                    } else if (type === '产品') {
                        if (typeof showProductHospitalBubbleModal === 'function') {
                            log(`🚀 调用 showProductHospitalBubbleModal(${bubbleData.label})`);
                            showProductHospitalBubbleModal(bubbleData.label);
                        } else {
                            log('❌ showProductHospitalBubbleModal 函数不存在');
                        }
                    }
                } else {
                    log('⚠️ 未找到有效的气泡点');
                }
            });

            log(`✅ ${type}图表创建完成`);
            return chart;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🎬 页面加载完成，开始初始化');
            
            // 等待 dashboard.js 加载完成
            setTimeout(() => {
                log('⏳ 开始创建测试图表');
                createChart('personnelChart', personnelTestData, '人员表现', '人员');
                createChart('productChart', productTestData, '产品表现', '产品');
                log('🎉 所有图表创建完成，可以开始测试双击功能');
            }, 1000);
        });
    </script>

    <!-- 引入气泡图弹窗功能 -->
    <script src="js/dashboard.js"></script>
</body>
</html>
