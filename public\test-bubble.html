<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气泡图双击测试</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-section {
            margin-bottom: 40px;
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .chart-container {
            position: relative;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>气泡图双击功能测试</h1>
        
        <div class="instructions">
            <h3>测试说明：</h3>
            <ul>
                <li>双击人员气泡 → 显示该人员的产品气泡图</li>
                <li>双击产品气泡 → 显示该产品在各医院的占比气泡图</li>
                <li>在弹窗中双击医院气泡 → 显示该医院的产品气泡图</li>
                <li>点击"查看80%销量医院"按钮 → 显示医院列表</li>
            </ul>
        </div>

        <div class="chart-section">
            <div class="chart-title">人员气泡图（双击测试）</div>
            <div class="chart-container">
                <canvas id="testPersonnelChart"></canvas>
            </div>
        </div>

        <div class="chart-section">
            <div class="chart-title">产品气泡图（双击测试）</div>
            <div class="chart-container">
                <canvas id="testProductChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        // 测试数据
        const testPersonnelData = [
            { x: 85, y: 15, r: 25, label: '张三', sales: 120000, achievement: 85, growth: 15, type: 'personnel' },
            { x: 92, y: -5, r: 30, label: '李四', sales: 150000, achievement: 92, growth: -5, type: 'personnel' },
            { x: 78, y: 25, r: 20, label: '王五', sales: 98000, achievement: 78, growth: 25, type: 'personnel' },
            { x: 105, y: 8, r: 35, label: '赵六', sales: 180000, achievement: 105, growth: 8, type: 'personnel' }
        ];

        const testProductData = [
            { x: 88, y: 12, r: 28, label: '产品A', sales: 200000, achievement: 88, growth: 12, type: 'product' },
            { x: 95, y: -3, r: 32, label: '产品B', sales: 250000, achievement: 95, growth: -3, type: 'product' },
            { x: 72, y: 18, r: 22, label: '产品C', sales: 160000, achievement: 72, growth: 18, type: 'product' },
            { x: 110, y: 5, r: 38, label: '产品D', sales: 300000, achievement: 110, growth: 5, type: 'product' }
        ];

        // 模拟API响应数据
        window.mockApiData = {
            personnelProducts: {
                '张三': [
                    { name: '产品A', actual: 50000, target: 60000, lastYearSales: 45000 },
                    { name: '产品B', actual: 70000, target: 65000, lastYearSales: 60000 }
                ],
                '李四': [
                    { name: '产品C', actual: 80000, target: 75000, lastYearSales: 70000 },
                    { name: '产品D', actual: 70000, target: 80000, lastYearSales: 65000 }
                ]
            },
            productHospitals: {
                '产品A': [
                    { name: '医院A', actual: 30000, lastYearSales: 25000 },
                    { name: '医院B', actual: 45000, lastYearSales: 40000 },
                    { name: '医院C', actual: 25000, lastYearSales: 30000 }
                ],
                '产品B': [
                    { name: '医院D', actual: 60000, lastYearSales: 55000 },
                    { name: '医院E', actual: 40000, lastYearSales: 35000 }
                ]
            },
            hospitalProducts: {
                '医院A': [
                    { name: '产品A', actual: 30000, target: 35000, lastYearSales: 25000 },
                    { name: '产品E', actual: 20000, target: 25000, lastYearSales: 18000 }
                ]
            }
        };

        // 重写API调用函数以使用模拟数据
        window.loadPersonnelProductData = async function(personnelName) {
            console.log('模拟加载人员产品数据:', personnelName);
            
            const products = window.mockApiData.personnelProducts[personnelName] || [];
            if (products.length > 0) {
                renderModalBubbleChart(products, 'product', `${personnelName} 的产品表现`);
            } else {
                showModalError('暂无该人员的产品数据');
            }
        };

        window.loadProductHospitalData = async function(productName) {
            console.log('模拟加载产品医院数据:', productName);
            
            const hospitals = window.mockApiData.productHospitals[productName] || [];
            if (hospitals.length > 0) {
                renderModalBubbleChart(hospitals, 'hospital', `${productName} 的医院分布`);
            } else {
                showModalError('暂无该产品的医院数据');
            }
        };

        window.loadHospitalProductData = async function(hospitalName) {
            console.log('模拟加载医院产品数据:', hospitalName);
            
            const products = window.mockApiData.hospitalProducts[hospitalName] || [];
            if (products.length > 0) {
                renderModalBubbleChart(products, 'product', `${hospitalName} 的产品表现`);
            } else {
                showModalError('暂无该医院的产品数据');
            }
        };

        window.showTop80HospitalsList = async function(filterName) {
            console.log('模拟显示80%销量医院列表:', filterName);
            
            // 模拟医院数据
            const hospitals = [
                { name: '医院A', actual: 300000 },
                { name: '医院B', actual: 250000 },
                { name: '医院C', actual: 200000 },
                { name: '医院D', actual: 150000 },
                { name: '医院E', actual: 100000 }
            ];
            
            displayHospitalsList(hospitals, filterName);
        };

        // 获取当前筛选条件（模拟）
        window.getSelectedFilters = function() {
            return {
                months: ['2024-01'],
                products: [],
                personnel: [],
                terminals: []
            };
        };
    </script>

    <!-- 引入气泡图弹窗功能 -->
    <script src="js/dashboard.js"></script>
    
    <script>
        // 创建测试图表的函数
        function createTestCharts() {
            // 创建人员气泡图
            const personnelCanvas = document.getElementById('testPersonnelChart');
            const personnelCtx = personnelCanvas.getContext('2d');
            
            const personnelChart = new Chart(personnelCtx, {
                type: 'bubble',
                data: {
                    datasets: [{
                        label: '人员表现',
                        data: testPersonnelData,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 205, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 205, 86, 1)',
                            'rgba(75, 192, 192, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '人员表现分析（双击气泡测试）'
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return context[0].raw.label;
                                },
                                label: function(context) {
                                    const data = context.raw;
                                    return [
                                        `销售金额: ¥${(data.sales / 10000).toFixed(1)}万`,
                                        `达成率: ${data.achievement.toFixed(1)}%`,
                                        `增长率: ${data.growth >= 0 ? '+' : ''}${data.growth.toFixed(1)}%`
                                    ];
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '达成率 (%)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '增长率 (%)'
                            }
                        }
                    }
                }
            });

            // 添加双击事件
            personnelCanvas.addEventListener('dblclick', function(event) {
                const points = personnelChart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);
                if (points.length > 0) {
                    const point = points[0];
                    const dataIndex = point.index;
                    const personnelData = personnelChart.data.datasets[0].data[dataIndex];
                    
                    console.log('双击人员气泡:', personnelData.label);
                    showPersonnelProductBubbleModal(personnelData.label);
                }
            });

            // 创建产品气泡图
            const productCanvas = document.getElementById('testProductChart');
            const productCtx = productCanvas.getContext('2d');
            
            const productChart = new Chart(productCtx, {
                type: 'bubble',
                data: {
                    datasets: [{
                        label: '产品表现',
                        data: testProductData,
                        backgroundColor: [
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)',
                            'rgba(83, 102, 255, 0.7)'
                        ],
                        borderColor: [
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)',
                            'rgba(199, 199, 199, 1)',
                            'rgba(83, 102, 255, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '产品表现分析（双击气泡测试）'
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return context[0].raw.label;
                                },
                                label: function(context) {
                                    const data = context.raw;
                                    return [
                                        `销售金额: ¥${(data.sales / 10000).toFixed(1)}万`,
                                        `达成率: ${data.achievement.toFixed(1)}%`,
                                        `增长率: ${data.growth >= 0 ? '+' : ''}${data.growth.toFixed(1)}%`
                                    ];
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '达成率 (%)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '增长率 (%)'
                            }
                        }
                    }
                }
            });

            // 添加双击事件
            productCanvas.addEventListener('dblclick', function(event) {
                const points = productChart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);
                if (points.length > 0) {
                    const point = points[0];
                    const dataIndex = point.index;
                    const productData = productChart.data.datasets[0].data[dataIndex];
                    
                    console.log('双击产品气泡:', productData.label);
                    showProductHospitalBubbleModal(productData.label);
                }
            });
        }

        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟创建图表，确保dashboard.js已加载
            setTimeout(createTestCharts, 500);
        });
    </script>
</body>
</html>
