<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单气泡图测试</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
            border: 1px solid #ddd;
            padding: 10px;
        }
        .test-info {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>气泡图双击功能测试</h1>
        
        <div class="test-info">
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>双击下面的气泡</li>
                <li>应该会弹出一个模态框</li>
                <li>模态框中显示相关的气泡图</li>
            </ol>
        </div>

        <div class="chart-container">
            <canvas id="testChart"></canvas>
        </div>

        <div id="log" style="background: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 20px;">
            <strong>日志：</strong><br>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            console.log(message);
        }

        // 测试数据
        const testData = [
            { x: 85, y: 15, r: 25, label: '张三', sales: 120000, achievement: 85, growth: 15 },
            { x: 92, y: -5, r: 30, label: '李四', sales: 150000, achievement: 92, growth: -5 }
        ];

        // 创建图表
        function createChart() {
            const canvas = document.getElementById('testChart');
            const ctx = canvas.getContext('2d');
            
            const chart = new Chart(ctx, {
                type: 'bubble',
                data: {
                    datasets: [{
                        label: '测试数据',
                        data: testData,
                        backgroundColor: ['rgba(255, 99, 132, 0.7)', 'rgba(54, 162, 235, 0.7)'],
                        borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)'],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '测试气泡图 - 双击气泡试试'
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '达成率 (%)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '增长率 (%)'
                            }
                        }
                    }
                }
            });

            // 添加双击事件
            canvas.addEventListener('dblclick', function(event) {
                log('检测到双击事件');
                
                const points = chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);
                log('找到的点数量: ' + points.length);
                
                if (points.length > 0) {
                    const point = points[0];
                    const dataIndex = point.index;
                    const data = chart.data.datasets[0].data[dataIndex];
                    
                    log('双击的数据: ' + data.label);
                    
                    // 测试弹窗函数
                    if (typeof showPersonnelProductBubbleModal === 'function') {
                        log('调用弹窗函数');
                        showPersonnelProductBubbleModal(data.label);
                    } else {
                        log('弹窗函数不存在，创建简单弹窗');
                        createSimpleModal(data.label);
                    }
                }
            });

            log('图表创建完成');
        }

        // 简单弹窗函数
        function createSimpleModal(name) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;
            
            modal.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; max-width: 400px;">
                    <h3>测试弹窗</h3>
                    <p>您双击了: <strong>${name}</strong></p>
                    <button onclick="this.closest('div').parentElement.remove()" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        关闭
                    </button>
                </div>
            `;
            
            document.body.appendChild(modal);
            log('简单弹窗已创建');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            createChart();
        });
    </script>
</body>
</html>
