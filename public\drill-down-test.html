<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气泡图钻取功能测试</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .header p {
            color: #666;
            font-size: 16px;
        }
        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .chart-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .chart-card:hover {
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,123,255,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #495057;
            text-align: center;
            padding: 10px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border-radius: 8px;
        }
        .chart-container {
            position: relative;
            height: 350px;
            background: white;
            border-radius: 8px;
            padding: 10px;
        }
        .instructions {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        .instructions h3 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 20px;
        }
        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }
        .instructions li {
            margin-bottom: 8px;
            font-size: 15px;
        }
        .test-log {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            padding: 20px;
            border-radius: 12px;
            max-height: 250px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 3px 0;
        }
        .log-time {
            color: #6c757d;
            font-weight: bold;
        }
        .log-action {
            color: #007bff;
            font-weight: bold;
        }
        .log-data {
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🎯 气泡图多层钻取功能测试</h1>
            <p>测试在弹窗中进行新的数据查询和绘制2D气泡图</p>
        </div>
        
        <div class="instructions">
            <h3>📋 测试流程说明</h3>
            <ul>
                <li><strong>第一层：</strong>双击人员气泡 → 弹出该人员的产品气泡图</li>
                <li><strong>第二层：</strong>在弹窗中双击产品气泡 → 弹出该产品在各医院的占比气泡图</li>
                <li><strong>第三层：</strong>在弹窗中双击医院气泡 → 弹出该医院的产品气泡图</li>
                <li><strong>特殊功能：</strong>点击"查看80%销量医院"按钮 → 显示医院列表</li>
            </ul>
        </div>

        <div class="chart-grid">
            <div class="chart-card">
                <div class="chart-title">👥 人员表现气泡图</div>
                <div class="chart-container">
                    <canvas id="personnelChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-title">📦 产品表现气泡图</div>
                <div class="chart-container">
                    <canvas id="productChart"></canvas>
                </div>
            </div>
        </div>

        <div class="test-log" id="testLog">
            <div class="log-entry">
                <span class="log-time">[系统]</span> 
                <span class="log-action">测试日志初始化完成</span>
            </div>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            let typeClass = 'log-action';
            if (type === 'data') typeClass = 'log-data';
            if (type === 'error') typeClass = 'text-danger';
            
            logEntry.innerHTML = `
                <span class="log-time">[${timestamp}]</span> 
                <span class="${typeClass}">${message}</span>
            `;
            
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        // 模拟筛选器函数
        window.getSelectedFilters = function() {
            log('调用 getSelectedFilters 函数', 'data');
            return {
                months: ['2024-01'],
                products: [],
                personnel: [],
                terminals: []
            };
        };

        // 模拟多选筛选器函数
        window.getMultiSelectValues = function(type) {
            log(`调用 getMultiSelectValues(${type})`, 'data');
            return [];
        };

        // 模拟API数据
        window.mockApiData = {
            personnelProducts: {
                '张三': [
                    { name: '产品A', actual: 50000, target: 60000, lastYearSales: 45000 },
                    { name: '产品B', actual: 70000, target: 65000, lastYearSales: 60000 },
                    { name: '产品C', actual: 40000, target: 45000, lastYearSales: 35000 }
                ],
                '李四': [
                    { name: '产品D', actual: 80000, target: 75000, lastYearSales: 70000 },
                    { name: '产品E', actual: 60000, target: 70000, lastYearSales: 55000 }
                ],
                '王五': [
                    { name: '产品F', actual: 45000, target: 50000, lastYearSales: 40000 },
                    { name: '产品G', actual: 55000, target: 60000, lastYearSales: 50000 }
                ]
            },
            productHospitals: {
                '产品A': [
                    { name: '医院A', actual: 30000, lastYearSales: 25000 },
                    { name: '医院B', actual: 45000, lastYearSales: 40000 },
                    { name: '医院C', actual: 25000, lastYearSales: 30000 }
                ],
                '产品B': [
                    { name: '医院D', actual: 60000, lastYearSales: 55000 },
                    { name: '医院E', actual: 40000, lastYearSales: 35000 },
                    { name: '医院F', actual: 35000, lastYearSales: 30000 }
                ]
            },
            hospitalProducts: {
                '医院A': [
                    { name: '产品A', actual: 30000, target: 35000, lastYearSales: 25000 },
                    { name: '产品H', actual: 20000, target: 25000, lastYearSales: 18000 }
                ],
                '医院B': [
                    { name: '产品A', actual: 45000, target: 50000, lastYearSales: 40000 },
                    { name: '产品I', actual: 25000, target: 30000, lastYearSales: 22000 }
                ]
            }
        };

        // 重写API调用函数
        window.loadPersonnelProductData = async function(personnelName) {
            log(`🔍 模拟加载人员产品数据: ${personnelName}`, 'action');
            
            const products = window.mockApiData.personnelProducts[personnelName] || [];
            if (products.length > 0) {
                log(`✅ 找到 ${products.length} 个产品数据`, 'data');
                renderModalBubbleChart(products, 'product', `${personnelName} 的产品表现`);
            } else {
                log(`❌ 未找到 ${personnelName} 的产品数据`, 'error');
                showModalError('暂无该人员的产品数据');
            }
        };

        window.loadProductHospitalData = async function(productName) {
            log(`🔍 模拟加载产品医院数据: ${productName}`, 'action');
            
            const hospitals = window.mockApiData.productHospitals[productName] || [];
            if (hospitals.length > 0) {
                log(`✅ 找到 ${hospitals.length} 个医院数据`, 'data');
                renderModalBubbleChart(hospitals, 'hospital', `${productName} 的医院分布`);
            } else {
                log(`❌ 未找到 ${productName} 的医院数据`, 'error');
                showModalError('暂无该产品的医院数据');
            }
        };

        window.loadHospitalProductData = async function(hospitalName) {
            log(`🔍 模拟加载医院产品数据: ${hospitalName}`, 'action');
            
            const products = window.mockApiData.hospitalProducts[hospitalName] || [];
            if (products.length > 0) {
                log(`✅ 找到 ${products.length} 个产品数据`, 'data');
                renderModalBubbleChart(products, 'product', `${hospitalName} 的产品表现`);
            } else {
                log(`❌ 未找到 ${hospitalName} 的产品数据`, 'error');
                showModalError('暂无该医院的产品数据');
            }
        };

        // 测试数据
        const personnelTestData = [
            { x: 85, y: 15, r: 25, label: '张三', sales: 120000, achievement: 85, growth: 15 },
            { x: 92, y: -5, r: 30, label: '李四', sales: 150000, achievement: 92, growth: -5 },
            { x: 78, y: 25, r: 20, label: '王五', sales: 98000, achievement: 78, growth: 25 }
        ];

        const productTestData = [
            { x: 88, y: 12, r: 28, label: '产品A', sales: 200000, achievement: 88, growth: 12 },
            { x: 95, y: -3, r: 32, label: '产品B', sales: 250000, achievement: 95, growth: -3 },
            { x: 72, y: 18, r: 22, label: '产品C', sales: 160000, achievement: 72, growth: 18 }
        ];

        // 创建图表函数
        function createChart(canvasId, data, title, type) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            const chart = new Chart(ctx, {
                type: 'bubble',
                data: {
                    datasets: [{
                        label: title,
                        data: data,
                        backgroundColor: data.map((_, i) => `hsla(${i * 120 + 200}, 70%, 60%, 0.8)`),
                        borderColor: data.map((_, i) => `hsla(${i * 120 + 200}, 70%, 50%, 1)`),
                        borderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${title} - 双击气泡进行钻取`,
                            font: { size: 14, weight: 'bold' }
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return context[0].raw.label;
                                },
                                label: function(context) {
                                    const data = context.raw;
                                    return [
                                        `销售金额: ¥${(data.sales / 10000).toFixed(1)}万`,
                                        `达成率: ${data.achievement}%`,
                                        `增长率: ${data.growth >= 0 ? '+' : ''}${data.growth}%`
                                    ];
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '达成率 (%)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '增长率 (%)'
                            }
                        }
                    }
                }
            });

            // 添加双击事件
            canvas.addEventListener('dblclick', function(event) {
                log(`🖱️ 检测到双击事件 - ${type}图表`, 'action');
                
                const points = chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);
                
                if (points.length > 0) {
                    const point = points[0];
                    const dataIndex = point.index;
                    const bubbleData = chart.data.datasets[0].data[dataIndex];
                    
                    log(`✨ 双击的${type}: ${bubbleData.label}`, 'action');
                    
                    // 根据类型调用不同的弹窗函数
                    if (type === '人员') {
                        if (typeof showPersonnelProductBubbleModal === 'function') {
                            log(`🚀 调用 showPersonnelProductBubbleModal(${bubbleData.label})`, 'action');
                            showPersonnelProductBubbleModal(bubbleData.label);
                        } else {
                            log('❌ showPersonnelProductBubbleModal 函数不存在', 'error');
                        }
                    } else if (type === '产品') {
                        if (typeof showProductHospitalBubbleModal === 'function') {
                            log(`🚀 调用 showProductHospitalBubbleModal(${bubbleData.label})`, 'action');
                            showProductHospitalBubbleModal(bubbleData.label);
                        } else {
                            log('❌ showProductHospitalBubbleModal 函数不存在', 'error');
                        }
                    }
                } else {
                    log('⚠️ 未找到有效的气泡点', 'error');
                }
            });

            log(`✅ ${type}图表创建完成`, 'data');
            return chart;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🎬 页面加载完成，开始初始化', 'action');
            
            // 等待 dashboard.js 加载完成
            setTimeout(() => {
                log('⏳ 开始创建测试图表', 'action');
                createChart('personnelChart', personnelTestData, '人员表现', '人员');
                createChart('productChart', productTestData, '产品表现', '产品');
                log('🎉 所有图表创建完成，可以开始测试多层钻取功能', 'action');
            }, 1000);
        });
    </script>

    <!-- 引入气泡图弹窗功能 -->
    <script src="js/dashboard.js"></script>
</body>
</html>
