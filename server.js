const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const path = require('path');
const multer = require('multer');
const XLSX = require('xlsx');
const fs = require('fs');
const os = require('os');
const { db, initDatabase } = require('./database/init');

// 通用增长率计算函数
function calculateGrowthRate(current, previous) {
    // 情况1：去年同期为0
    if (previous === 0) {
        // 无论当期是多少，都显示"-"，因为无法计算增长率
        return "-";
    }

    // 情况2：去年同期为正数（正常情况）
    if (previous > 0) {
        const growthRate = ((current - previous) / previous) * 100;
        return `${growthRate >= 0 ? '+' : ''}${growthRate.toFixed(1)}%`;
    }

    // 情况3：去年同期为负数（特殊处理）
    if (previous < 0) {
        if (current >= 0) {
            // 从负数变为正数：这是改善，计算改善幅度
            // 使用绝对值作为基数：(当期 - 去年同期) / |去年同期| * 100
            const improvementRate = ((current - previous) / Math.abs(previous)) * 100;
            return `+${improvementRate.toFixed(1)}%`;
        } else {
            // 都是负数：比较亏损程度变化
            const absChange = Math.abs(current) - Math.abs(previous);
            const changeRate = (absChange / Math.abs(previous)) * 100;

            if (absChange > 0) {
                // 亏损增加
                return `-${changeRate.toFixed(1)}%`;
            } else if (absChange < 0) {
                // 亏损减少（改善）
                return `+${Math.abs(changeRate).toFixed(1)}%`;
            } else {
                return `0.0%`;
            }
        }
    }

    return "-";
}

const app = express();
const PORT = process.env.PORT || 3001;
const JWT_SECRET = 'your-secret-key-change-in-production';

// 中间件
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' })); // 增加JSON请求体大小限制
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' })); // 增加URL编码请求体大小限制

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 配置文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, 'uploads');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    fileFilter: function (req, file, cb) {
        const allowedTypes = ['.xlsx', '.xls', '.csv'];
        const fileExt = path.extname(file.originalname).toLowerCase();
        if (allowedTypes.includes(fileExt)) {
            cb(null, true);
        } else {
            cb(new Error('只支持 Excel (.xlsx, .xls) 和 CSV 文件'));
        }
    },
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB
    }
});

// 初始化数据库
initDatabase();

// JWT验证中间件
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: '访问令牌缺失' });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            console.error('JWT验证失败:', err);
            return res.status(403).json({ error: '无效的访问令牌' });
        }
        console.log('JWT验证成功，用户信息:', JSON.stringify(user, null, 2));
        req.user = user;
        next();
    });
};

// 管理员权限验证中间件
const requireAdmin = (req, res, next) => {
    if (req.user.role !== 'admin') {
        return res.status(403).json({ error: '需要管理员权限' });
    }
    next();
};

// 登录接口
app.post('/api/login', (req, res) => {
    const { username, password } = req.body;

    if (!username || !password) {
        return res.status(400).json({ error: '用户名和密码不能为空' });
    }

    const query = 'SELECT * FROM users WHERE username = ?';
    db.get(query, [username], (err, user) => {
        if (err) {
            console.error('数据库查询错误:', err);
            return res.status(500).json({ error: '服务器内部错误' });
        }

        if (!user) {
            return res.status(401).json({ error: '用户名或密码错误' });
        }

        // 验证密码
        bcrypt.compare(password, user.password, (err, isMatch) => {
            if (err) {
                console.error('密码验证错误:', err);
                return res.status(500).json({ error: '服务器内部错误' });
            }

            if (!isMatch) {
                return res.status(401).json({ error: '用户名或密码错误' });
            }

            // 生成JWT令牌
            const token = jwt.sign(
                {
                    id: user.id,
                    username: user.username,
                    role: user.role,
                    full_name: user.full_name
                },
                JWT_SECRET,
                { expiresIn: '24h' }
            );

            res.json({
                success: true,
                message: '登录成功',
                token: token,
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    full_name: user.full_name,
                    role: user.role,
                    department: user.department
                }
            });
        });
    });
});

// 获取用户信息
app.get('/api/user', authenticateToken, (req, res) => {
    const query = 'SELECT id, username, email, full_name, role, department, created_at FROM users WHERE id = ?';
    db.get(query, [req.user.id], (err, user) => {
        if (err) {
            console.error('获取用户信息错误:', err);
            return res.status(500).json({ error: '服务器内部错误' });
        }

        if (!user) {
            return res.status(404).json({ error: '用户不存在' });
        }

        res.json({ 
            success: true, 
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                full_name: user.full_name,
                role: user.role,
                department: user.department,
                created_at: user.created_at
            }
        });
    });
});

// 获取销售数据统计
app.get('/api/sales/stats', authenticateToken, (req, res) => {
    const { employee } = req.query; // 管理员可以查看指定员工的数据

    console.log('Stats API called by user:', req.user.username, 'role:', req.user.role);

    // 构建WHERE条件 - 从 salestable 表获取数据
    let whereCondition = '销售金额折算后 > 0';
    let params = [];

    if (req.user.role === 'admin' && employee) {
        // 管理员查看指定员工数据
        whereCondition += ' AND 上传人 = ?';
        params.push(employee);
        console.log('Admin viewing employee data:', employee);
    } else if (req.user.role !== 'admin') {
        // 普通用户只能查看自己的数据
        whereCondition += ' AND 上传人 = ?';
        params.push(req.user.full_name || req.user.username);
        console.log('User viewing own data:', req.user.full_name || req.user.username);
    } else {
        console.log('Admin viewing all data');
    }

    const queries = {
        totalSales: `SELECT SUM(销售金额折算后) as total FROM salestable WHERE ${whereCondition}`,
        totalOrders: `SELECT COUNT(*) as count FROM salestable WHERE ${whereCondition}`,
        avgOrderValue: `SELECT AVG(销售金额折算后) as avg FROM salestable WHERE ${whereCondition}`,
        totalQuantity: `SELECT SUM(销量盒折算后) as total FROM salestable WHERE ${whereCondition}`,
        topProducts: `
            SELECT 产品, SUM(销量盒折算后) as total_quantity, SUM(销售金额折算后) as total_revenue, COUNT(*) as order_count
            FROM salestable
            WHERE ${whereCondition}
            GROUP BY 产品
            ORDER BY total_revenue DESC
            LIMIT 5
        `,
        topCustomers: `
            SELECT 医院, 最终客户编码, SUM(销售金额折算后) as total_revenue, COUNT(*) as order_count
            FROM salestable
            WHERE ${whereCondition}
            GROUP BY 最终客户编码, 医院
            ORDER BY total_revenue DESC
            LIMIT 5
        `,
        monthlyTrend: `
            SELECT 年份, 月份, SUM(销售金额折算后) as monthly_sales, SUM(销量盒折算后) as monthly_quantity
            FROM salestable
            WHERE ${whereCondition}
            GROUP BY 年份, 月份
            ORDER BY 年份 DESC, 月份 DESC
            LIMIT 12
        `,
        regionStats: `
            SELECT 城市, COUNT(*) as order_count, SUM(销售金额折算后) as total_sales, SUM(销量盒折算后) as total_quantity
            FROM salestable
            WHERE ${whereCondition}
            GROUP BY 城市
            ORDER BY total_sales DESC
            LIMIT 10
        `
    };

    const results = {};
    let completed = 0;
    const totalQueries = Object.keys(queries).length;

    Object.entries(queries).forEach(([key, query]) => {
        db.all(query, params, (err, rows) => {
            if (err) {
                console.error(`查询${key}错误:`, err);
                return res.status(500).json({ error: '服务器内部错误' });
            }

            results[key] = rows;
            completed++;

            if (completed === totalQueries) {
                res.json({
                    success: true,
                    data: {
                        totalSales: results.totalSales[0]?.total || 0,
                        totalOrders: results.totalOrders[0]?.count || 0,
                        avgOrderValue: results.avgOrderValue[0]?.avg || 0,
                        totalQuantity: results.totalQuantity[0]?.total || 0,
                        topProducts: results.topProducts || [],
                        topCustomers: results.topCustomers || [],
                        monthlyTrend: results.monthlyTrend || [],
                        regionStats: results.regionStats || []
                    }
                });
            }
        });
    });
});

// 获取用户年度数据汇总
app.get('/api/sales/user-summary', authenticateToken, (req, res) => {
    const { year = new Date().getFullYear() } = req.query;

    console.log('User summary API called by user:', req.user.username, 'for year:', year);

    // 构建WHERE条件 - 只查看当前用户上传的数据
    let whereCondition = '上传人 = ?';
    let params = [req.user.username];

    // 如果指定了年份，添加年份过滤（默认查询所有年份的数据）
    if (year && year !== '2025') {
        whereCondition += ' AND 年份 = ?';
        params.push(year.toString());
    }

    // 获取基础统计数据
    const statsQuery = `
        SELECT
            COUNT(*) as totalRecords,
            SUM(CAST(销售金额折算后 AS REAL)) as totalSales,
            SUM(CAST(指标金额 AS REAL)) as totalTarget,
            COUNT(DISTINCT 产品) as totalProducts,
            COUNT(DISTINCT 最终客户编码) as totalCustomers
        FROM salestable
        WHERE ${whereCondition}
    `;

    db.get(statsQuery, params, (err, stats) => {
        if (err) {
            console.error('获取统计数据失败:', err);
            return res.status(500).json({ success: false, message: '获取统计数据失败' });
        }

        console.log('统计数据查询结果:', stats);

        // 计算达成率
        const achievementRate = stats.totalTarget > 0 ? (stats.totalSales / stats.totalTarget * 100) : 0;
        console.log('达成率计算:', achievementRate);

        // 获取月度趋势数据
        const monthlyQuery = `
            SELECT
                SUBSTR(CAST(月份 AS TEXT), 5, 2) as month,
                SUM(CAST(销售金额折算后 AS REAL)) as sales,
                SUM(CAST(指标金额 AS REAL)) as target
            FROM salestable
            WHERE ${whereCondition}
            GROUP BY SUBSTR(CAST(月份 AS TEXT), 5, 2)
            ORDER BY SUBSTR(CAST(月份 AS TEXT), 5, 2)
        `;

        db.all(monthlyQuery, params, (err, monthlyData) => {
            if (err) {
                console.error('获取月度数据失败:', err);
                return res.status(500).json({ success: false, message: '获取月度数据失败' });
            }

            // 获取产品排行
            const topProductsQuery = `
                SELECT
                    产品 as productCode,
                    产品 as productName,
                    SUM(CAST(销售金额折算后 AS REAL)) as totalSales,
                    SUM(CAST(指标金额 AS REAL)) as totalTarget
                FROM salestable
                WHERE ${whereCondition}
                GROUP BY 产品
                ORDER BY totalSales DESC
                LIMIT 10
            `;

            console.log('产品查询SQL:', topProductsQuery);
            console.log('查询参数:', params);
            db.all(topProductsQuery, params, (err, topProducts) => {
                if (err) {
                    console.error('获取产品排行失败:', err);
                    return res.status(500).json({ success: false, message: '获取产品排行失败' });
                }

                console.log('产品排行查询结果:', topProducts);

                // 返回汇总数据
                res.json({
                    success: true,
                    data: {
                        year: year,
                        totalSales: stats.totalSales || 0,
                        totalTarget: stats.totalTarget || 0,
                        totalRecords: stats.totalRecords || 0,
                        totalProducts: stats.totalProducts || 0,
                        totalCustomers: stats.totalCustomers || 0,
                        achievementRate: achievementRate,
                        monthlyData: monthlyData || [],
                        topProducts: topProducts || []
                    }
                });
            });
        });
    });
});

// 获取销售数据列表
app.get('/api/sales', authenticateToken, (req, res) => {
    const {
        page = 1,
        limit = 20,
        products,
        months,
        quarters,
        terminals,
        regionProductLines,
        city,
        customer,
        year,
        employee,
        reps
    } = req.query;
    const offset = (page - 1) * limit;

    let query = 'SELECT * FROM salestable WHERE 1=1';
    let countQuery = 'SELECT COUNT(*) as total FROM salestable WHERE 1=1';
    const params = [];

    // 数据权限控制
    if (req.user.role === 'admin' && employee) {
        query += ' AND 上传人 = ?';
        countQuery += ' AND 上传人 = ?';
        params.push(employee);
    } else if (req.user.role !== 'admin') {
        // 普通用户只能查看自己的数据
        // 优先使用 rep_code，如果没有则使用 full_name，最后使用 username
        const userRepCode = req.user.rep_code || req.user.full_name || req.user.username;
        query += ' AND 新REP = ?';
        countQuery += ' AND 新REP = ?';
        params.push(userRepCode);
    } else if (req.user.role === 'admin' && reps) {
        // 管理员可以筛选指定的销售员
        const repList = Array.isArray(reps) ? reps : reps.split(',');
        if (repList.length > 0) {
            const placeholders = repList.map(() => '?').join(',');
            query += ` AND 新REP IN (${placeholders})`;
            countQuery += ` AND 新REP IN (${placeholders})`;
            params.push(...repList);
        }
    }

    // 多选产品筛选
    if (products) {
        const productList = Array.isArray(products) ? products : products.split(',');
        if (productList.length > 0) {
            const placeholders = productList.map(() => '?').join(',');
            query += ` AND 产品 IN (${placeholders})`;
            countQuery += ` AND 产品 IN (${placeholders})`;
            params.push(...productList);
        }
    }

    // 多选月份筛选
    if (months) {
        const monthList = Array.isArray(months) ? months : months.split(',');
        if (monthList.length > 0) {
            const monthConditions = monthList.map(() => 'SUBSTR(PRINTF("%06d", 月份), -2) = ?').join(' OR ');
            query += ` AND (${monthConditions})`;
            countQuery += ` AND (${monthConditions})`;
            params.push(...monthList);
        }
    }

    // 多选季度筛选
    if (quarters) {
        const quarterList = Array.isArray(quarters) ? quarters : quarters.split(',');
        if (quarterList.length > 0) {
            const quarterConditions = quarterList.map(() => '季度 LIKE ?').join(' OR ');
            query += ` AND (${quarterConditions})`;
            countQuery += ` AND (${quarterConditions})`;
            quarterList.forEach(q => params.push(`%${q}%`));
        }
    }

    // 多选医院筛选
    if (terminals) {
        const terminalList = Array.isArray(terminals) ? terminals : terminals.split(',');
        if (terminalList.length > 0) {
            const placeholders = terminalList.map(() => '?').join(',');
            query += ` AND 医院 IN (${placeholders})`;
            countQuery += ` AND 医院 IN (${placeholders})`;
            params.push(...terminalList);
        }
    }

    // 多选地区产品线筛选
    if (regionProductLines) {
        const regionProductLineList = Array.isArray(regionProductLines) ? regionProductLines : regionProductLines.split(',');
        if (regionProductLineList.length > 0) {
            const placeholders = regionProductLineList.map(() => '?').join(',');
            query += ` AND 地区产品线 IN (${placeholders})`;
            countQuery += ` AND 地区产品线 IN (${placeholders})`;
            params.push(...regionProductLineList);
        }
    }

    // 单选城市筛选
    if (city) {
        query += ' AND 城市 = ?';
        countQuery += ' AND 城市 = ?';
        params.push(city);
    }

    // 客户搜索
    if (customer) {
        query += ' AND (医院 LIKE ? OR 最终客户编码 LIKE ?)';
        countQuery += ' AND (医院 LIKE ? OR 最终客户编码 LIKE ?)';
        params.push(`%${customer}%`, `%${customer}%`);
    }

    // 年份筛选
    if (year) {
        query += ' AND 年份 = ?';
        countQuery += ' AND 年份 = ?';
        params.push(parseInt(year));
    }

    query += ' ORDER BY 年份 DESC, 月份 DESC, 销售金额折算后 DESC LIMIT ? OFFSET ?';
    const queryParams = [...params, parseInt(limit), parseInt(offset)];

    // 获取总数
    db.get(countQuery, params, (err, countResult) => {
        if (err) {
            console.error('查询销售数据总数错误:', err);
            return res.status(500).json({ error: '服务器内部错误' });
        }

        // 获取数据
        db.all(query, queryParams, (err, rows) => {
            if (err) {
                console.error('查询销售数据错误:', err);
                return res.status(500).json({ error: '服务器内部错误' });
            }

            res.json({
                success: true,
                data: rows,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total: countResult.total,
                    totalPages: Math.ceil(countResult.total / limit)
                }
            });
        });
    });
});

// 获取筛选选项（原始版本，加载所有选项）
app.get('/api/sales/filters', authenticateToken, (req, res) => {
    console.log('=== 获取筛选选项请求 ===');
    // 构建WHERE条件 - 普通用户也可以看到所有筛选选项，只在实际数据查询时进行权限控制
    let whereCondition = '1=1';
    let params = [];

    // 注释掉：普通用户也需要看到所有筛选选项
    // if (req.user.role !== 'admin') {
    //     whereCondition += ' AND 上传人 = ?';
    //     params.push(req.user.username);
    // }

    console.log('用户角色:', req.user.role, '- 加载所有筛选选项');

    // 构建销售员查询条件 - 根据用户角色区分
    let repsWhereCondition = '1=1';
    let repsParams = [];

    if (req.user.role === 'admin') {
        // 管理员可以看到所有销售员
        repsWhereCondition = '1=1';
        console.log('管理员权限：加载所有销售员选项');
    } else {
        // 普通用户只能看到与自己 full_name 匹配的销售员
        const userRepCode = req.user.full_name || req.user.username;
        repsWhereCondition = '新REP = ?';
        repsParams = [userRepCode];
        console.log('普通用户权限：只加载匹配的销售员', userRepCode);
    }

    const queries = {
        products: `
            SELECT
                MIN(产品编码concat) as 产品编码concat,
                产品
            FROM salestable
            WHERE ${whereCondition} AND 产品 IS NOT NULL AND 产品编码concat IS NOT NULL
            GROUP BY 产品
            ORDER BY 产品
        `,
        cities: `SELECT DISTINCT 城市 FROM salestable WHERE ${whereCondition} AND 城市 IS NOT NULL ORDER BY 城市`,
        years: `SELECT DISTINCT 年份 FROM salestable WHERE ${whereCondition} AND 年份 IS NOT NULL ORDER BY 年份 DESC`,
        months: `
            SELECT DISTINCT 月份
            FROM salestable
            WHERE ${whereCondition} AND 月份 IS NOT NULL
            ORDER BY 月份
        `,
        quarters: `SELECT DISTINCT 季度 FROM salestable WHERE ${whereCondition} AND 季度 IS NOT NULL ORDER BY 季度`,
        terminals: `SELECT DISTINCT 医院 FROM salestable WHERE ${whereCondition} AND 医院 IS NOT NULL ORDER BY 医院`,
        reps: `SELECT DISTINCT 新REP FROM salestable WHERE ${repsWhereCondition} AND 新REP IS NOT NULL ORDER BY 新REP`,
        regionProductLines: `SELECT DISTINCT 地区产品线 FROM salestable WHERE ${whereCondition} AND 地区产品线 IS NOT NULL AND 地区产品线 != '' ORDER BY 地区产品线`
    };

    const results = {};
    let completed = 0;
    const totalQueries = Object.keys(queries).length;

    Object.entries(queries).forEach(([key, query]) => {
        // 为销售员查询使用特殊的参数
        const queryParams = key === 'reps' ? repsParams : params;

        db.all(query, queryParams, (err, rows) => {
            if (err) {
                console.error(`查询${key}错误:`, err);
                return res.status(500).json({ error: '服务器内部错误' });
            }

            results[key] = rows;
            completed++;

            if (completed === totalQueries) {
                console.log('筛选选项查询结果:', JSON.stringify(results, null, 2));
                res.json({
                    success: true,
                    data: results
                });
            }
        });
    });
});

// 获取关联筛选选项（根据已选择的条件动态过滤）
app.post('/api/sales/filters/related', authenticateToken, (req, res) => {
    console.log('=== 获取关联筛选选项请求 ===');
    const { products, months, quarters, terminals, reps, regionProductLines } = req.body;

    console.log('已选择的筛选条件:', { products, months, quarters, terminals, reps, regionProductLines });

    // 构建基础WHERE条件（权限控制）
    let baseWhereCondition = '1=1';
    let baseParams = [];

    // 权限控制
    if (req.user.role !== 'admin') {
        const userRepCode = req.user.rep_code || req.user.full_name || req.user.username;
        baseWhereCondition += ' AND 新REP = ?';
        baseParams.push(userRepCode);
    }

    // 构建动态WHERE条件（基于已选择的筛选条件）
    function buildDynamicWhereCondition(excludeField) {
        let whereCondition = baseWhereCondition;
        let params = [...baseParams];

        // 产品筛选
        if (excludeField !== 'products' && products && products.length > 0) {
            const placeholders = products.map(() => '?').join(',');
            whereCondition += ` AND 产品 IN (${placeholders})`;
            params.push(...products);
        }

        // 月份筛选
        if (excludeField !== 'months' && months && months.length > 0) {
            const monthConditions = months.map(month => {
                if (String(month).length === 6) {
                    return `月份 = ${month}`;
                } else {
                    const currentYear = new Date().getFullYear();
                    const fullMonth = `${currentYear}${String(month).padStart(2, '0')}`;
                    return `月份 = ${fullMonth}`;
                }
            });
            if (monthConditions.length > 0) {
                whereCondition += ` AND (${monthConditions.join(' OR ')})`;
            }
        }

        // 季度筛选
        if (excludeField !== 'quarters' && quarters && quarters.length > 0) {
            const quarterConditions = quarters.map(quarter => `季度 = '${quarter}'`);
            if (quarterConditions.length > 0) {
                whereCondition += ` AND (${quarterConditions.join(' OR ')})`;
            }
        }

        // 医院筛选
        if (excludeField !== 'terminals' && terminals && terminals.length > 0) {
            const placeholders = terminals.map(() => '?').join(',');
            whereCondition += ` AND 医院 IN (${placeholders})`;
            params.push(...terminals);
        }

        // 销售员筛选（管理员才能筛选）
        if (excludeField !== 'reps' && req.user.role === 'admin' && reps && reps.length > 0) {
            const placeholders = reps.map(() => '?').join(',');
            whereCondition += ` AND 新REP IN (${placeholders})`;
            params.push(...reps);
        }

        // 地区产品线筛选
        if (excludeField !== 'regionProductLines' && regionProductLines && regionProductLines.length > 0) {
            const placeholders = regionProductLines.map(() => '?').join(',');
            whereCondition += ` AND 地区产品线 IN (${placeholders})`;
            params.push(...regionProductLines);
        }

        return { whereCondition, params };
    }

    // 构建查询 - 返回所有筛选器的选项
    const queries = {};
    const queryParams = {};

    // 产品选项（基于其他筛选条件）
    const productFilter = buildDynamicWhereCondition('products');
    queries.products = `
        SELECT
            MIN(产品编码concat) as 产品编码concat,
            产品
        FROM salestable
        WHERE ${productFilter.whereCondition} AND 产品 IS NOT NULL AND 产品编码concat IS NOT NULL
        GROUP BY 产品
        ORDER BY 产品
    `;
    queryParams.products = productFilter.params;

    // 月份选项（基于其他筛选条件）
    const monthFilter = buildDynamicWhereCondition('months');
    queries.months = `
        SELECT DISTINCT 月份
        FROM salestable
        WHERE ${monthFilter.whereCondition} AND 月份 IS NOT NULL
        ORDER BY 月份
    `;
    queryParams.months = monthFilter.params;

    // 季度选项（基于其他筛选条件）
    const quarterFilter = buildDynamicWhereCondition('quarters');
    queries.quarters = `
        SELECT DISTINCT 季度
        FROM salestable
        WHERE ${quarterFilter.whereCondition} AND 季度 IS NOT NULL
        ORDER BY 季度
    `;
    queryParams.quarters = quarterFilter.params;

    // 医院选项（基于其他筛选条件）
    const terminalFilter = buildDynamicWhereCondition('terminals');
    queries.terminals = `
        SELECT DISTINCT 医院
        FROM salestable
        WHERE ${terminalFilter.whereCondition} AND 医院 IS NOT NULL
        ORDER BY 医院
    `;
    queryParams.terminals = terminalFilter.params;

    // 销售员选项（基于其他筛选条件，管理员才能看到）
    if (req.user.role === 'admin') {
        const repFilter = buildDynamicWhereCondition('reps');
        queries.reps = `
            SELECT DISTINCT 新REP
            FROM salestable
            WHERE ${repFilter.whereCondition} AND 新REP IS NOT NULL
            ORDER BY 新REP
        `;
        queryParams.reps = repFilter.params;
    }

    // 地区产品线选项（基于其他筛选条件）
    const regionFilter = buildDynamicWhereCondition('regionProductLines');
    queries.regionProductLines = `
        SELECT DISTINCT 地区产品线
        FROM salestable
        WHERE ${regionFilter.whereCondition} AND 地区产品线 IS NOT NULL AND 地区产品线 != ''
        ORDER BY 地区产品线
    `;
    queryParams.regionProductLines = regionFilter.params;

    console.log('执行关联查询，查询数量:', Object.keys(queries).length);

    const results = {};
    let completed = 0;
    const totalQueries = Object.keys(queries).length;

    if (totalQueries === 0) {
        return res.json({
            success: true,
            data: {}
        });
    }

    Object.entries(queries).forEach(([key, query]) => {
        console.log(`执行${key}查询:`, query);
        console.log(`${key}参数:`, queryParams[key]);

        db.all(query, queryParams[key], (err, rows) => {
            if (err) {
                console.error(`查询关联${key}错误:`, err);
                return res.status(500).json({ error: '服务器内部错误' });
            }

            results[key] = rows;
            console.log(`${key}查询完成，结果数量:`, rows.length);
            completed++;

            if (completed === totalQueries) {
                console.log('所有关联筛选选项查询完成');
                console.log('结果统计:', Object.keys(results).map(key => `${key}: ${results[key].length}`).join(', '));
                res.json({
                    success: true,
                    data: results
                });
            }
        });
    });
});

// 获取医院选项
app.get('/api/sales/terminals', authenticateToken, (req, res) => {
    let whereCondition = '1=1';
    let params = [];

    // 注释掉：普通用户也需要看到所有医院选项
    // if (req.user.role !== 'admin') {
    //     whereCondition += ' AND 上传人 = ?';
    //     params.push(req.user.username);
    // }

    const query = `
        SELECT DISTINCT 医院
        FROM salestable
        WHERE ${whereCondition} AND 医院 IS NOT NULL AND 医院 != ''
        ORDER BY 医院
        LIMIT 100
    `;

    db.all(query, params, (err, rows) => {
        if (err) {
            console.error('查询医院选项错误:', err);
            return res.status(500).json({ error: '服务器内部错误' });
        }

        res.json({
            success: true,
            data: rows
        });
    });
});

// 根据产品编码和客户编码获取详细信息
app.get('/api/sales/detail/:productCode/:customerCode', authenticateToken, (req, res) => {
    const { productCode, customerCode } = req.params;

    let whereCondition = '产品编码concat = ? AND 最终客户编码 = ?';
    let params = [productCode, customerCode];

    // 数据权限控制
    if (req.user.role !== 'admin') {
        whereCondition += ' AND 上传人 = ?';
        params.push(req.user.username);
    }

    const query = `
        SELECT * FROM salestable
        WHERE ${whereCondition}
        ORDER BY 年份 DESC, 月份 DESC
    `;

    db.all(query, params, (err, rows) => {
        if (err) {
            console.error('查询销售详情错误:', err);
            return res.status(500).json({ error: '服务器内部错误' });
        }

        res.json({
            success: true,
            data: rows
        });
    });
});

// 用户管理 - 获取所有用户（仅管理员）
app.get('/api/users', authenticateToken, requireAdmin, (req, res) => {
    const { page = 1, limit = 20, search = '' } = req.query;
    const offset = (page - 1) * limit;

    let query = 'SELECT id, username, email, full_name, role, department, is_active, created_at FROM users WHERE 1=1';
    let countQuery = 'SELECT COUNT(*) as total FROM users WHERE 1=1';
    const params = [];

    if (search) {
        query += ' AND (username LIKE ? OR full_name LIKE ? OR email LIKE ?)';
        countQuery += ' AND (username LIKE ? OR full_name LIKE ? OR email LIKE ?)';
        const searchParam = `%${search}%`;
        params.push(searchParam, searchParam, searchParam);
    }

    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    const queryParams = [...params, parseInt(limit), parseInt(offset)];

    // 获取总数
    db.get(countQuery, params, (err, countResult) => {
        if (err) {
            console.error('查询用户总数错误:', err);
            return res.status(500).json({ error: '服务器内部错误' });
        }

        // 获取用户列表
        db.all(query, queryParams, (err, rows) => {
            if (err) {
                console.error('查询用户列表错误:', err);
                return res.status(500).json({ error: '服务器内部错误' });
            }

            res.json({
                success: true,
                data: rows,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total: countResult.total,
                    totalPages: Math.ceil(countResult.total / limit)
                }
            });
        });
    });
});

// 创建用户（仅管理员）
app.post('/api/users', authenticateToken, requireAdmin, (req, res) => {
    const { username, password, email, full_name, role = 'user', department } = req.body;

    if (!username || !password || !full_name) {
        return res.status(400).json({ error: '用户名、密码和真实姓名不能为空' });
    }

    const hashedPassword = bcrypt.hashSync(password, 10);

    const insertUser = `
        INSERT INTO users (username, password, email, full_name, role, department)
        VALUES (?, ?, ?, ?, ?, ?)
    `;

    db.run(insertUser, [username, hashedPassword, email, full_name, role, department], function(err) {
        if (err) {
            if (err.message.includes('UNIQUE constraint failed')) {
                return res.status(400).json({ error: '用户名已存在' });
            }
            console.error('创建用户错误:', err);
            return res.status(500).json({ error: '服务器内部错误' });
        }

        res.json({
            success: true,
            message: '用户创建成功',
            user: {
                id: this.lastID,
                username,
                email,
                full_name,
                role,
                department
            }
        });
    });
});

// 更新用户（仅管理员）
app.put('/api/users/:id', authenticateToken, requireAdmin, (req, res) => {
    const { id } = req.params;
    const { email, full_name, role, department, is_active } = req.body;

    console.log('更新用户请求:', {
        id: id,
        body: req.body,
        extracted: { email, full_name, role, department, is_active }
    });

    const updateUser = `
        UPDATE users
        SET email = ?, full_name = ?, role = ?, department = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    `;

    console.log('执行SQL:', updateUser);
    console.log('参数:', [email, full_name, role, department, is_active, id]);

    db.run(updateUser, [email, full_name, role, department, is_active, id], function(err) {
        if (err) {
            console.error('更新用户错误:', err);
            return res.status(500).json({ error: '服务器内部错误' });
        }

        console.log('更新结果:', { changes: this.changes, lastID: this.lastID });

        if (this.changes === 0) {
            return res.status(404).json({ error: '用户不存在' });
        }

        res.json({
            success: true,
            message: '用户更新成功'
        });
    });
});

// 重置用户密码（仅管理员）
app.post('/api/users/:id/reset-password', authenticateToken, requireAdmin, (req, res) => {
    const { id } = req.params;
    const { password } = req.body;

    if (!password) {
        return res.status(400).json({ error: '密码不能为空' });
    }

    const hashedPassword = bcrypt.hashSync(password, 10);

    const updatePassword = 'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';

    db.run(updatePassword, [hashedPassword, id], function(err) {
        if (err) {
            console.error('重置密码错误:', err);
            return res.status(500).json({ error: '服务器内部错误' });
        }

        if (this.changes === 0) {
            return res.status(404).json({ error: '用户不存在' });
        }

        res.json({
            success: true,
            message: '密码重置成功'
        });
    });
});

// 获取单个用户信息（仅管理员）
app.get('/api/users/:id', authenticateToken, requireAdmin, (req, res) => {
    const { id } = req.params;

    const query = 'SELECT id, username, email, full_name, role, department, is_active, created_at FROM users WHERE id = ?';
    db.get(query, [id], (err, user) => {
        if (err) {
            console.error('查询用户信息错误:', err);
            return res.status(500).json({ error: '服务器内部错误' });
        }

        if (!user) {
            return res.status(404).json({ error: '用户不存在' });
        }

        res.json({
            success: true,
            data: user
        });
    });
});

// 删除用户（仅管理员）
app.delete('/api/users/:id', authenticateToken, requireAdmin, (req, res) => {
    const { id } = req.params;

    // 不允许删除管理员账户
    const checkUserQuery = 'SELECT role FROM users WHERE id = ?';
    db.get(checkUserQuery, [id], (err, user) => {
        if (err) {
            console.error('查询用户角色错误:', err);
            return res.status(500).json({ error: '服务器内部错误' });
        }

        if (!user) {
            return res.status(404).json({ error: '用户不存在' });
        }

        if (user.role === 'admin') {
            return res.status(400).json({ error: '不能删除管理员账户' });
        }

        // 删除用户
        const deleteUser = 'DELETE FROM users WHERE id = ?';
        db.run(deleteUser, [id], function(err) {
            if (err) {
                console.error('删除用户错误:', err);
                return res.status(500).json({ error: '服务器内部错误' });
            }

            if (this.changes === 0) {
                return res.status(404).json({ error: '用户不存在' });
            }

            res.json({
                success: true,
                message: '用户删除成功'
            });
        });
    });
});

// 获取员工列表（用于管理员筛选）
app.get('/api/employees', authenticateToken, requireAdmin, (req, res) => {
    const query = 'SELECT username, full_name FROM users WHERE role = "user" AND is_active = 1 ORDER BY full_name';

    db.all(query, [], (err, rows) => {
        if (err) {
            console.error('查询员工列表错误:', err);
            return res.status(500).json({ error: '服务器内部错误' });
        }

        res.json({
            success: true,
            data: rows
        });
    });
});

// 数据导入 - 上传文件
app.post('/api/import/upload', authenticateToken, upload.single('file'), (req, res) => {
    if (!req.file) {
        return res.status(400).json({ error: '请选择要上传的文件' });
    }

    try {
        const filePath = req.file.path;
        const fileExt = path.extname(req.file.originalname).toLowerCase();

        let data;

        if (fileExt === '.csv') {
            // 处理CSV文件
            const csvData = fs.readFileSync(filePath, 'utf8');
            const lines = csvData.split('\n');
            const headers = lines[0].split(',').map(h => h.trim());

            data = lines.slice(1).filter(line => line.trim()).map(line => {
                const values = line.split(',').map(v => v.trim());
                const row = {};
                headers.forEach((header, index) => {
                    row[header] = values[index] || '';
                });
                return row;
            });
        } else {
            // 处理Excel文件
            const workbook = XLSX.readFile(filePath);
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            data = XLSX.utils.sheet_to_json(worksheet);
        }

        // 清理临时文件
        fs.unlinkSync(filePath);

        // 验证数据格式
        const validationResult = validateImportData(data);
        if (!validationResult.valid) {
            return res.status(400).json({
                error: '数据格式验证失败',
                details: validationResult.errors
            });
        }

        res.json({
            success: true,
            message: '文件解析成功',
            data: {
                totalRows: data.length,
                preview: data.slice(0, 5), // 返回前5行预览
                fullData: data, // 返回完整数据
                columns: Object.keys(data[0] || {})
            }
        });

    } catch (error) {
        console.error('文件处理错误:', error);

        // 清理临时文件
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        res.status(500).json({ error: '文件处理失败: ' + error.message });
    }
});

// 数据导入 - 确认导入
app.post('/api/import/confirm', authenticateToken, (req, res) => {
    const { data, mapping } = req.body;

    if (!data || !Array.isArray(data) || data.length === 0) {
        return res.status(400).json({ error: '导入数据不能为空' });
    }

    // 开始事务
    db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        const insertQuery = `
            INSERT INTO salestable (
                城市, 产品编码concat, 地区产品线, 备注, 产品, 参考价, 县域, 最终客户编码,
                医院, 新REP, 原始客户名称终端, 年份, 月份, 季度, 销量盒折算后, 销售金额折算后,
                指标盒折算后, 指标金额, 上传人, 上传时间
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const currentTime = new Date().toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        data.forEach((row, index) => {
            try {
                // 映射数据字段
                const mappedRow = mapRowData(row, mapping);

                // 系统自动填充的字段
                const insertData = [
                    mappedRow.城市 || '',
                    mappedRow.产品编码concat || '',
                    mappedRow.地区产品线 || '',
                    mappedRow.备注 || '',
                    mappedRow.产品 || '',
                    parseFloat(mappedRow.参考价) || 0,
                    mappedRow.县域 || '',
                    mappedRow.最终客户编码 || '',
                    mappedRow.医院 || '',
                    mappedRow.新REP || '',
                    mappedRow.原始客户名称终端 || '',
                    parseInt(mappedRow.年份) || new Date().getFullYear(),
                    parseInt(mappedRow.月份) || new Date().getMonth() + 1,
                    mappedRow.季度 || '',
                    parseFloat(mappedRow.销量盒折算后) || 0,
                    parseFloat(mappedRow.销售金额折算后) || 0,
                    parseFloat(mappedRow.指标盒折算后) || 0,
                    parseFloat(mappedRow.指标金额) || 0,
                    req.user.username, // 上传人
                    currentTime // 上传时间
                ];

                db.run(insertQuery, insertData, function(err) {
                    if (err) {
                        errorCount++;
                        errors.push(`第${index + 1}行: ${err.message}`);
                    } else {
                        successCount++;
                    }

                    // 检查是否所有行都处理完成
                    if (successCount + errorCount === data.length) {
                        if (errorCount === 0) {
                            db.run('COMMIT', (err) => {
                                if (err) {
                                    console.error('提交事务失败:', err);
                                    res.status(500).json({ error: '数据导入失败' });
                                } else {
                                    res.json({
                                        success: true,
                                        message: `数据导入成功，共导入 ${successCount} 条记录`,
                                        stats: { successCount, errorCount, errors }
                                    });
                                }
                            });
                        } else {
                            db.run('ROLLBACK', () => {
                                res.status(400).json({
                                    error: `数据导入失败，发现 ${errorCount} 个错误`,
                                    stats: { successCount, errorCount, errors }
                                });
                            });
                        }
                    }
                });

            } catch (error) {
                errorCount++;
                errors.push(`第${index + 1}行: ${error.message}`);
            }
        });
    });
});

// 验证导入数据格式
function validateImportData(data) {
    const errors = [];

    if (!data || data.length === 0) {
        errors.push('数据不能为空');
        return { valid: false, errors };
    }

    // 检查必要字段
    const requiredFields = ['产品编码concat', '最终客户编码'];
    const firstRow = data[0];
    const availableFields = Object.keys(firstRow);

    requiredFields.forEach(field => {
        if (!availableFields.includes(field)) {
            errors.push(`缺少必要字段: ${field}`);
        }
    });

    return {
        valid: errors.length === 0,
        errors
    };
}

// 映射行数据
function mapRowData(row, mapping) {
    if (!mapping) {
        return row; // 如果没有映射配置，直接返回原数据
    }

    const mappedRow = {};
    Object.keys(mapping).forEach(targetField => {
        const sourceField = mapping[targetField];
        mappedRow[targetField] = row[sourceField] || '';
    });

    return mappedRow;
}

// 清空销售数据表
app.post('/api/clear-sales-data', authenticateToken, requireAdmin, (req, res) => {
    console.log('=== 清空销售数据表请求 ===');
    console.log('操作用户:', req.user.username, '角色:', req.user.role);

    // 首先查询当前数据量
    const countQuery = 'SELECT COUNT(*) as count FROM salestable';

    db.get(countQuery, [], (err, countResult) => {
        if (err) {
            console.error('查询数据量失败:', err);
            return res.status(500).json({
                success: false,
                error: '查询数据量失败'
            });
        }

        const totalCount = countResult.count;
        console.log('当前数据表中共有', totalCount, '条记录');

        if (totalCount === 0) {
            return res.json({
                success: true,
                message: '数据表已经是空的',
                deletedCount: 0
            });
        }

        // 执行清空操作
        const deleteQuery = 'DELETE FROM salestable';

        db.run(deleteQuery, [], function(err) {
            if (err) {
                console.error('清空数据表失败:', err);
                return res.status(500).json({
                    success: false,
                    error: '清空数据表失败: ' + err.message
                });
            }

            console.log('数据表清空成功，删除了', totalCount, '条记录');

            // 重置自增ID
            const resetQuery = 'DELETE FROM sqlite_sequence WHERE name = "salestable"';
            db.run(resetQuery, [], (resetErr) => {
                if (resetErr) {
                    console.warn('重置自增ID失败:', resetErr);
                    // 不影响主要操作，只记录警告
                }

                res.json({
                    success: true,
                    message: '数据表清空成功',
                    deletedCount: totalCount
                });
            });
        });
    });
});

// 获取图表数据
function getChartData(whereClause, params, res) {
    console.log('=== getChartData函数调用 ===');
    console.log('WHERE条件:', whereClause);
    console.log('参数:', params);

    const queries = {
        // 总体完成情况
        overall: `
            SELECT
                SUM(CASE WHEN 销售金额折算后 > 0 THEN 销售金额折算后 ELSE 0 END) as completed,
                SUM(CASE WHEN 指标金额 > 销售金额折算后 THEN 指标金额 - 销售金额折算后 ELSE 0 END) as remaining
            FROM salestable
            WHERE ${whereClause}
        `,

        // 同比数据
        yearOverYear: `
            SELECT
                SUM(CASE WHEN 年份 = strftime('%Y', 'now') THEN 销售金额折算后 ELSE 0 END) as current,
                SUM(CASE WHEN 年份 = strftime('%Y', 'now') - 1 THEN 销售金额折算后 ELSE 0 END) as previous
            FROM salestable
            WHERE ${whereClause}
        `,

        // 占比数据
        share: `
            SELECT 产品 as name, SUM(销售金额折算后) as value
            FROM salestable
            WHERE ${whereClause} AND 产品 IS NOT NULL
            GROUP BY 产品
            ORDER BY value DESC
            LIMIT 10
        `,

        // 环比数据
        monthOverMonth: `
            SELECT
                SUM(CASE WHEN 月份 = strftime('%m', 'now') THEN 销售金额折算后 ELSE 0 END) as current,
                SUM(CASE WHEN 月份 = strftime('%m', 'now') - 1 THEN 销售金额折算后 ELSE 0 END) as previous
            FROM salestable
            WHERE ${whereClause}
        `,

        // 达成情况
        achievement: `
            SELECT
                产品 as name,
                SUM(销售金额折算后) as actual,
                SUM(指标金额) as target
            FROM salestable
            WHERE ${whereClause} AND 产品 IS NOT NULL
            GROUP BY 产品
            ORDER BY actual DESC
            LIMIT 10
        `,

        // 销售趋势
        salesTrend: `
            SELECT
                年份 || '-' || CASE WHEN 月份 < 10 THEN '0' || 月份 ELSE 月份 END as period,
                SUM(销售金额折算后) as sales,
                SUM(指标金额) as target
            FROM salestable
            WHERE ${whereClause}
            GROUP BY 年份, 月份
            ORDER BY 年份, 月份
            LIMIT 12
        `,

        // 产品对比
        productComparison: `
            SELECT
                产品 as product,
                SUM(销售金额折算后) as sales
            FROM salestable
            WHERE ${whereClause} AND 产品 IS NOT NULL
            GROUP BY 产品
            ORDER BY sales DESC
            LIMIT 8
        `
    };

    const results = {};
    let completed = 0;
    const totalQueries = Object.keys(queries).length;

    Object.entries(queries).forEach(([key, query]) => {
        console.log(`执行查询 ${key}:`, query);
        db.all(query, params, (err, rows) => {
            if (err) {
                console.error(`查询${key}错误:`, err);
                return res.status(500).json({ error: '获取图表数据失败' });
            }

            console.log(`查询${key}结果:`, rows);

            // 处理特殊数据格式
            switch (key) {
                case 'overall':
                case 'yearOverYear':
                case 'monthOverMonth':
                    results[key] = rows[0] || {};
                    break;
                case 'share':
                case 'achievement':
                case 'salesTrend':
                case 'productComparison':
                    results[key] = rows || [];
                    break;
                default:
                    results[key] = rows || [];
            }

            completed++;

            if (completed === totalQueries) {
                // 添加计算字段
                results.achievementDistribution = calculateAchievementDistribution(results.achievement);
                results.targetVsActual = calculateTargetVsActual(results.achievement);
                results.performanceComparison = generatePerformanceComparison(results.salesTrend);
                results.yearComparison = generateYearComparison(results.salesTrend);
                results.achievementTrend = calculateAchievementTrend(results.salesTrend);

                res.json(results);
            }
        });
    });
}

// 计算达成率分布
function calculateAchievementDistribution(achievementData) {
    const distribution = {
        '0-50%': 0,
        '50-80%': 0,
        '80-100%': 0,
        '100%+': 0
    };

    achievementData.forEach(item => {
        const rate = item.target > 0 ? (item.actual / item.target) * 100 : 0;
        if (rate < 50) {
            distribution['0-50%']++;
        } else if (rate < 80) {
            distribution['50-80%']++;
        } else if (rate < 100) {
            distribution['80-100%']++;
        } else {
            distribution['100%+']++;
        }
    });

    return Object.entries(distribution).map(([range, count]) => ({
        range,
        count
    }));
}

// 计算目标vs实际
function calculateTargetVsActual(achievementData) {
    const totalActual = achievementData.reduce((sum, item) => sum + (item.actual || 0), 0);
    const totalTarget = achievementData.reduce((sum, item) => sum + (item.target || 0), 0);

    return {
        achieved: totalActual,
        remaining: Math.max(0, totalTarget - totalActual)
    };
}

// 生成业绩对比数据
function generatePerformanceComparison(salesTrendData) {
    const currentYear = new Date().getFullYear();
    const lastYear = currentYear - 1;

    const currentYearData = salesTrendData.filter(item =>
        item.period.startsWith(currentYear.toString())
    );

    return currentYearData.map((item, index) => {
        const month = item.period.split('-')[1];
        const lastYearPeriod = `${lastYear}-${month}`;
        const lastYearItem = salesTrendData.find(data => data.period === lastYearPeriod);

        return {
            period: item.period,
            current: item.sales || 0,
            previous: lastYearItem ? lastYearItem.sales || 0 : 0
        };
    });
}

// 新的数据比较逻辑函数
// 计算同期比较数据（去年同期）
function calculateSamePeriodComparison(selectedMonths, selectedQuarters, whereClause, params) {
    let currentPeriodQuery = '';
    let samePeriodQuery = '';
    let currentParams = [...params];
    let samePeriodParams = [...params];

    if (selectedMonths && selectedMonths.length > 0) {
        // 月份同期比较：选择的月份对应去年同月份
        const monthConditions = selectedMonths.map(month => {
            if (String(month).length === 6) {
                // 6位数格式 (如202401)
                const year = parseInt(String(month).substring(0, 4));
                const monthNum = parseInt(String(month).substring(4, 6));
                const lastYearMonth = parseInt(`${year - 1}${String(monthNum).padStart(2, '0')}`);
                return { current: `月份 = ${month}`, samePeriod: `月份 = ${lastYearMonth}` };
            } else {
                // 简单月份数字，需要结合年份
                const currentYear = new Date().getFullYear();
                const currentMonth = parseInt(`${currentYear}${String(month).padStart(2, '0')}`);
                const lastYearMonth = parseInt(`${currentYear - 1}${String(month).padStart(2, '0')}`);
                return { current: `月份 = ${currentMonth}`, samePeriod: `月份 = ${lastYearMonth}` };
            }
        });

        currentPeriodQuery = `(${monthConditions.map(m => m.current).join(' OR ')})`;
        samePeriodQuery = `(${monthConditions.map(m => m.samePeriod).join(' OR ')})`;
    }

    if (selectedQuarters && selectedQuarters.length > 0) {
        // 季度同期比较：选择的季度对应去年同季度
        const quarterConditions = selectedQuarters.map(quarter => {
            const year = parseInt(quarter.substring(0, 4));
            const q = quarter.substring(4); // Q1, Q2, Q3, Q4
            const lastYearQuarter = `${year - 1}${q}`;

            return { current: `季度 = '${quarter}'`, samePeriod: `季度 = '${lastYearQuarter}'` };
        });

        const currentQuarterQuery = `(${quarterConditions.map(q => q.current).join(' OR ')})`;
        const samePeriodQuarterQuery = `(${quarterConditions.map(q => q.samePeriod).join(' OR ')})`;

        if (currentPeriodQuery) {
            currentPeriodQuery += ` OR ${currentQuarterQuery}`;
            samePeriodQuery += ` OR ${samePeriodQuarterQuery}`;
        } else {
            currentPeriodQuery = currentQuarterQuery;
            samePeriodQuery = samePeriodQuarterQuery;
        }
    }

    // 如果没有时间条件，返回空查询
    if (!currentPeriodQuery || !samePeriodQuery) {
        return {
            currentQuery: `SELECT 0 as current`,
            samePeriodQuery: `SELECT 0 as previous`,
            currentParams: [],
            samePeriodParams: []
        };
    }

    return {
        currentQuery: `SELECT SUM(销售金额折算后) as current FROM salestable WHERE ${whereClause} AND ${currentPeriodQuery}`,
        samePeriodQuery: `SELECT SUM(销售金额折算后) as previous FROM salestable WHERE ${whereClause} AND ${samePeriodQuery}`,
        currentParams,
        samePeriodParams
    };
}

// 计算环比比较数据（往前推相同时长）
function calculateSequentialComparison(selectedMonths, selectedQuarters, whereClause, params) {
    let currentPeriodQuery = '';
    let previousPeriodQuery = '';
    let currentParams = [...params];
    let previousParams = [...params];

    if (selectedMonths && selectedMonths.length > 0) {
        // 月份环比比较：往前推相同数量的月份
        const monthCount = selectedMonths.length;
        const monthConditions = [];
        const previousMonthConditions = [];

        // 构建当期月份条件
        selectedMonths.forEach(month => {
            monthConditions.push(`月份 = ${month}`);
        });

        // 计算上期月份：从最早的月份开始往前推相同数量的月份
        // 例如：选择202501-202506（6个月），上期应该是202507-202512（从202501往前推6个月）
        const sortedMonths = [...selectedMonths].sort();
        const earliestMonth = sortedMonths[0]; // 最早的月份

        if (String(earliestMonth).length === 6) {
            let year = parseInt(String(earliestMonth).substring(0, 4));
            let month = parseInt(String(earliestMonth).substring(4, 6));

            // 从最早月份开始，往前推monthCount个月
            for (let i = 0; i < monthCount; i++) {
                month--;
                if (month === 0) {
                    month = 12;
                    year--;
                }
            }

            // 然后连续取monthCount个月
            for (let i = 0; i < monthCount; i++) {
                const prevMonthStr = `${year}${String(month).padStart(2, '0')}`;
                previousMonthConditions.push(`月份 = ${prevMonthStr}`);

                month++;
                if (month === 13) {
                    month = 1;
                    year++;
                }
            }
        } else {
            // 处理简单月份数字的情况
            const currentYear = new Date().getFullYear();
            let year = currentYear;
            let month = parseInt(earliestMonth);

            // 从最早月份开始，往前推monthCount个月
            for (let i = 0; i < monthCount; i++) {
                month--;
                if (month === 0) {
                    month = 12;
                    year--;
                }
            }

            // 然后连续取monthCount个月
            for (let i = 0; i < monthCount; i++) {
                const prevMonthStr = `${year}${String(month).padStart(2, '0')}`;
                previousMonthConditions.push(`月份 = ${prevMonthStr}`);

                month++;
                if (month === 13) {
                    month = 1;
                    year++;
                }
            }
        }

        currentPeriodQuery = `(${monthConditions.join(' OR ')})`;
        previousPeriodQuery = `(${previousMonthConditions.join(' OR ')})`;

        console.log('环比计算 - 选择的月份:', selectedMonths);
        console.log('环比计算 - 最早月份:', earliestMonth);
        console.log('环比计算 - 上期月份条件:', previousMonthConditions);
    }

    if (selectedQuarters && selectedQuarters.length > 0) {
        // 季度环比比较：往前推一个季度
        const quarterConditions = [];
        const previousQuarterConditions = [];

        selectedQuarters.forEach(quarter => {
            const year = parseInt(quarter.substring(0, 4));
            const qNum = parseInt(quarter.substring(5, 6)); // Q1->1, Q2->2, etc.
            quarterConditions.push(`季度 = '${quarter}'`);

            // 计算上一个季度
            let prevYear = year;
            let prevQ = qNum - 1;
            if (prevQ <= 0) {
                prevQ = 4;
                prevYear -= 1;
            }
            const prevQuarter = `${prevYear}Q${prevQ}`;
            previousQuarterConditions.push(`季度 = '${prevQuarter}'`);
        });

        const currentQuarterQuery = `(${quarterConditions.join(' OR ')})`;
        const previousQuarterQuery = `(${previousQuarterConditions.join(' OR ')})`;

        if (currentPeriodQuery) {
            currentPeriodQuery += ` OR ${currentQuarterQuery}`;
            previousPeriodQuery += ` OR ${previousQuarterQuery}`;
        } else {
            currentPeriodQuery = currentQuarterQuery;
            previousPeriodQuery = previousQuarterQuery;
        }
    }

    // 如果没有时间条件，返回空查询
    if (!currentPeriodQuery || !previousPeriodQuery) {
        return {
            currentQuery: `SELECT 0 as current`,
            previousQuery: `SELECT 0 as previous`,
            currentParams: [],
            previousParams: []
        };
    }

    return {
        currentQuery: `SELECT SUM(销售金额折算后) as current FROM salestable WHERE ${whereClause} AND ${currentPeriodQuery}`,
        previousQuery: `SELECT SUM(销售金额折算后) as previous FROM salestable WHERE ${whereClause} AND ${previousPeriodQuery}`,
        currentParams,
        previousParams
    };
}

// 获取每个产品的比较数据
function getProductComparisonData(productRows, whereCondition, params, selectedMonths, selectedQuarters, callback) {
    console.log('=== 获取每个产品的比较数据 ===');

    let completedProducts = 0;
    const totalProducts = productRows.length;
    const productsWithComparison = [];

    if (totalProducts === 0) {
        return callback(null, []);
    }

    productRows.forEach((product, index) => {
        // 为每个产品构建WHERE条件
        const productWhereCondition = `${whereCondition} AND 产品 = ?`;
        const productParams = [...params, product.productName];

        // 获取该产品的同期和环比数据
        getProductSamePeriodData(productWhereCondition, productParams, selectedMonths, selectedQuarters, (err, samePeriodData) => {
            if (err) {
                console.error(`获取产品 ${product.productName} 同期数据失败:`, err);
                samePeriodData = { current: 0, previous: 0 };
            }

            getProductSequentialData(productWhereCondition, productParams, selectedMonths, selectedQuarters, (err, sequentialData) => {
                if (err) {
                    console.error(`获取产品 ${product.productName} 环比数据失败:`, err);
                    sequentialData = { current: 0, previous: 0 };
                }

                // 计算增长率
                const samePeriodGrowth = calculateGrowthRate(
                    samePeriodData.current,
                    samePeriodData.previous
                );

                const sequentialGrowth = calculateGrowthRate(
                    sequentialData.current,
                    sequentialData.previous
                );

                // 构建产品数据
                const productWithComparison = {
                    name: product.productName || '未知产品',
                    productCode: product.productCode,
                    actual: product.actualAmount || 0,
                    target: product.targetAmount || 0,
                    lastYearSales: samePeriodData.previous || 0,
                    lastPeriodSales: sequentialData.previous || 0,
                    actualQuantity: product.actualQuantity || 0,
                    samePeriodGrowth: samePeriodGrowth,
                    sequentialGrowth: sequentialGrowth
                };

                productsWithComparison[index] = productWithComparison;
                completedProducts++;

                // 检查是否所有产品都完成了
                if (completedProducts === totalProducts) {
                    callback(null, productsWithComparison);
                }
            });
        });
    });
}

// 获取产品同期数据
function getProductSamePeriodData(whereCondition, params, selectedMonths, selectedQuarters, callback) {
    const samePeriodComparison = calculateSamePeriodComparison(selectedMonths, selectedQuarters, whereCondition, params);

    // 执行当期数据查询
    db.get(samePeriodComparison.currentQuery, samePeriodComparison.currentParams, (err, currentResult) => {
        if (err) {
            return callback(err);
        }

        // 执行同期数据查询
        db.get(samePeriodComparison.samePeriodQuery, samePeriodComparison.samePeriodParams, (err, samePeriodResult) => {
            if (err) {
                return callback(err);
            }

            callback(null, {
                current: currentResult?.current || 0,
                previous: samePeriodResult?.previous || 0
            });
        });
    });
}

// 获取产品环比数据
function getProductSequentialData(whereCondition, params, selectedMonths, selectedQuarters, callback) {
    const sequentialComparison = calculateSequentialComparison(selectedMonths, selectedQuarters, whereCondition, params);

    // 执行当期数据查询
    db.get(sequentialComparison.currentQuery, sequentialComparison.currentParams, (err, currentResult) => {
        if (err) {
            return callback(err);
        }

        // 执行环比数据查询
        db.get(sequentialComparison.sequentialQuery, sequentialComparison.sequentialParams, (err, sequentialResult) => {
            if (err) {
                return callback(err);
            }

            callback(null, {
                current: currentResult?.current || 0,
                previous: sequentialResult?.previous || 0
            });
        });
    });
}

// 获取销售分析的比较数据
function getSalesAnalysisComparisonData(whereCondition, params, selectedMonths, selectedQuarters, callback) {
    console.log('=== 获取销售分析比较数据 ===');
    console.log('基础WHERE条件:', whereCondition);
    console.log('选择的月份:', selectedMonths);
    console.log('选择的季度:', selectedQuarters);

    const results = {};
    let completedQueries = 0;
    let hasCalledBack = false; // 防止多次回调
    const totalQueries = 2; // 同期、环比（同比与同期相同）

    // 检查是否所有查询都完成
    function checkCompletion() {
        if (completedQueries === totalQueries && !hasCalledBack) {
            hasCalledBack = true;
            // 同比数据与同期数据相同
            results.yearOverYear = results.samePeriod;
            callback(null, results);
        }
    }

    // 安全的错误处理函数
    function handleError(err) {
        if (!hasCalledBack) {
            hasCalledBack = true;
            // 返回默认值而不是错误，让调用方继续处理
            callback(null, {
                samePeriod: { current: 0, previous: 0 },
                sequential: { current: 0, previous: 0 },
                yearOverYear: { current: 0, previous: 0 }
            });
        }
    }

    // 1. 同期比较（去年同期）
    const samePeriodComparison = calculateSamePeriodComparison(selectedMonths, selectedQuarters, whereCondition, params);

    console.log('同期比较查询:');
    console.log('当期查询:', samePeriodComparison.currentQuery);
    console.log('同期查询:', samePeriodComparison.samePeriodQuery);
    console.log('当期参数:', samePeriodComparison.currentParams);
    console.log('同期参数:', samePeriodComparison.samePeriodParams);

    // 执行当期数据查询
    db.get(samePeriodComparison.currentQuery, samePeriodComparison.currentParams, (err, currentResult) => {
        if (err) {
            console.error('当期数据查询错误:', err);
            return handleError(err);
        }

        console.log('当期查询结果:', currentResult);

        // 执行同期数据查询
        db.get(samePeriodComparison.samePeriodQuery, samePeriodComparison.samePeriodParams, (err, samePeriodResult) => {
            if (err) {
                console.error('同期数据查询错误:', err);
                return handleError(err);
            }

            console.log('同期查询结果:', samePeriodResult);

            results.samePeriod = {
                current: currentResult?.current || 0,
                previous: samePeriodResult?.previous || 0
            };

            console.log('同期比较结果:', results.samePeriod);

            completedQueries++;
            checkCompletion();
        });
    });

    // 2. 环比比较（往前推相同时长）
    const sequentialComparison = calculateSequentialComparison(selectedMonths, selectedQuarters, whereCondition, params);

    console.log('环比比较查询:');
    console.log('当期查询:', sequentialComparison.currentQuery);
    console.log('环比查询:', sequentialComparison.previousQuery);
    console.log('当期参数:', sequentialComparison.currentParams);
    console.log('环比参数:', sequentialComparison.previousParams);

    // 执行当期数据查询
    db.get(sequentialComparison.currentQuery, sequentialComparison.currentParams, (err, currentResult) => {
        if (err) {
            console.error('环比当期数据查询错误:', err);
            return handleError(err);
        }

        console.log('环比当期查询结果:', currentResult);

        // 执行环比数据查询
        db.get(sequentialComparison.previousQuery, sequentialComparison.previousParams, (err, previousResult) => {
            if (err) {
                console.error('环比数据查询错误:', err);
                return handleError(err);
            }

            console.log('环比查询结果:', previousResult);

            results.sequential = {
                current: currentResult?.current || 0,
                previous: previousResult?.previous || 0
            };

            console.log('环比比较结果:', results.sequential);

            completedQueries++;
            checkCompletion();
        });
    });

    // 注意：同比数据将在前面的查询完成后设置
}

// 生成年度对比数据
function generateYearComparison(salesTrendData) {
    const currentYear = new Date().getFullYear();
    const lastYear = currentYear - 1;

    const monthlyData = {};

    salesTrendData.forEach(item => {
        const [year, month] = item.period.split('-');
        if (!monthlyData[month]) {
            monthlyData[month] = {};
        }
        monthlyData[month][year] = item.sales || 0;
    });

    return Object.entries(monthlyData).map(([month, yearData]) => ({
        month: `${month}月`,
        currentYear: yearData[currentYear] || 0,
        previousYear: yearData[lastYear] || 0
    }));
}

// 计算达成率趋势
function calculateAchievementTrend(salesTrendData) {
    return salesTrendData.map(item => ({
        period: item.period,
        rate: item.target > 0 ? ((item.sales || 0) / item.target * 100).toFixed(1) : 0
    }));
}

// 获取产品列表
app.get('/api/products', authenticateToken, (req, res) => {
    let whereCondition = '1=1';
    let params = [];

    // 注释掉：普通用户也需要看到所有产品
    // if (req.user.role !== 'admin') {
    //     whereCondition += ' AND 上传人 = ?';
    //     params.push(req.user.username);
    // }

    const query = `
        SELECT DISTINCT 产品 as product_code, 产品 as product_name
        FROM salestable
        WHERE ${whereCondition} AND 产品 IS NOT NULL
        ORDER BY 产品
    `;

    db.all(query, params, (err, rows) => {
        if (err) {
            console.error('获取产品列表失败:', err);
            res.status(500).json({ error: '获取产品列表失败' });
        } else {
            res.json(rows);
        }
    });
});




// 获取图表数据
app.get('/api/chart-data', authenticateToken, (req, res) => {
    console.log('=== 图表数据API调用 ===');
    console.log('用户:', req.user);
    console.log('查询参数:', req.query);

    const { timeRange, startDate, endDate, products, users } = req.query;

    // 构建基础查询条件
    let whereConditions = ['1=1'];
    let params = [];

    // 用户权限控制
    if (req.user.role !== 'admin') {
        whereConditions.push('上传人 = ?');
        params.push(req.user.username);
        console.log('普通用户，只查看自己的数据:', req.user.username);
    } else if (users) {
        const userList = users.split(',').map(u => u.trim()).filter(u => u);
        if (userList.length > 0) {
            const userPlaceholders = userList.map(() => '?').join(',');
            whereConditions.push(`上传人 IN (${userPlaceholders})`);
            params.push(...userList);
            console.log('管理员查看指定用户数据:', userList);
        } else {
            console.log('管理员查看所有用户数据');
        }
    } else {
        console.log('管理员查看所有用户数据');
    }

    // 产品筛选
    if (products) {
        const productList = products.split(',').map(p => p.trim()).filter(p => p);
        if (productList.length > 0) {
            const productPlaceholders = productList.map(() => '?').join(',');
            whereConditions.push(`产品 IN (${productPlaceholders})`);
            params.push(...productList);
        }
    }

    // 时间范围筛选
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;

    switch (timeRange) {
        case 'current_month':
            whereConditions.push('年份 = ? AND 月份 = ?');
            params.push(currentYear, currentMonth);
            break;
        case 'last_month':
            const lastMonth = currentMonth === 1 ? 12 : currentMonth - 1;
            const lastMonthYear = currentMonth === 1 ? currentYear - 1 : currentYear;
            whereConditions.push('年份 = ? AND 月份 = ?');
            params.push(lastMonthYear, lastMonth);
            break;
        case 'current_quarter':
            const currentQuarter = Math.ceil(currentMonth / 3);
            const quarterStart = (currentQuarter - 1) * 3 + 1;
            const quarterEnd = currentQuarter * 3;
            whereConditions.push('年份 = ? AND 月份 BETWEEN ? AND ?');
            params.push(currentYear, quarterStart, quarterEnd);
            break;
        case 'current_year':
            whereConditions.push('年份 = ?');
            params.push(currentYear);
            break;
        case 'custom':
            if (startDate && endDate) {
                whereConditions.push('日期 BETWEEN ? AND ?');
                params.push(startDate, endDate);
            }
            break;
    }

    const whereClause = whereConditions.join(' AND ');
    console.log('最终WHERE条件:', whereClause);
    console.log('查询参数:', params);

    // 获取图表数据
    getChartData(whereClause, params, res);
});

// 销售分析API
app.post('/api/sales-analysis', authenticateToken, (req, res) => {
    const { products, months, quarters, terminals, reps, regionProductLines } = req.body;

    console.log('=== 销售分析API调用 ===');
    console.log('用户:', req.user.username, '角色:', req.user.role);
    console.log('JWT token中的完整用户信息:', JSON.stringify(req.user, null, 2));
    console.log('筛选条件:', { products, months, quarters, terminals, reps, regionProductLines });
    console.log('请求体完整内容:', JSON.stringify(req.body, null, 2));

    // 构建基础WHERE条件（不包含时间筛选）
    let baseWhereCondition = '1=1';
    let baseParams = [];

    // 权限控制
    console.log('=== 权限控制调试 ===');
    console.log('用户角色:', req.user.role);
    console.log('角色类型:', typeof req.user.role);
    console.log('是否为管理员:', req.user.role === 'admin');
    console.log('角色比较结果:', req.user.role !== 'admin');

    // 临时修复：强制将admin用户识别为管理员
    const isAdmin = req.user.role === 'admin' || req.user.username === 'admin';
    console.log('修正后是否为管理员:', isAdmin);

    if (!isAdmin) {
        // 普通用户只能查看自己的数据
        // 优先使用 rep_code，如果没有则使用 full_name，最后使用 username
        const userRepCode = req.user.rep_code || req.user.full_name || req.user.username;
        baseWhereCondition += ' AND 新REP = ?';
        baseParams.push(userRepCode);
        console.log('普通用户权限控制，只查看自己的数据，匹配字段:', userRepCode);
    } else {
        // 管理员可以筛选指定的销售员
        console.log('管理员权限确认，可以查看所有数据');
        if (reps && reps.length > 0) {
            console.log('管理员筛选指定销售员:', reps);
            const placeholders = reps.map(() => '?').join(',');
            baseWhereCondition += ` AND 新REP IN (${placeholders})`;
            baseParams.push(...reps);
        } else {
            console.log('管理员查看所有销售员数据');
        }
    }

    // 产品筛选 - 现在按产品名称筛选
    if (products && products.length > 0) {
        const placeholders = products.map(() => '?').join(',');
        baseWhereCondition += ` AND 产品 IN (${placeholders})`;
        baseParams.push(...products);
    }

    // 医院筛选
    if (terminals && terminals.length > 0) {
        console.log('应用医院筛选，医院列表:', terminals);
        const placeholders = terminals.map(() => '?').join(',');
        baseWhereCondition += ` AND 医院 IN (${placeholders})`;
        baseParams.push(...terminals);
        console.log('医院筛选后的基础WHERE条件:', baseWhereCondition);
        console.log('医院筛选后的基础参数:', baseParams);
    } else {
        console.log('没有医院筛选条件');
    }

    // 地区产品线筛选
    if (regionProductLines && regionProductLines.length > 0) {
        console.log('应用地区产品线筛选，地区产品线列表:', regionProductLines);
        const placeholders = regionProductLines.map(() => '?').join(',');
        baseWhereCondition += ` AND 地区产品线 IN (${placeholders})`;
        baseParams.push(...regionProductLines);
        console.log('地区产品线筛选后的基础WHERE条件:', baseWhereCondition);
        console.log('地区产品线筛选后的基础参数:', baseParams);
    } else {
        console.log('没有地区产品线筛选条件');
    }

    // 构建完整WHERE条件（包含时间筛选，用于当期数据查询）
    let fullWhereCondition = baseWhereCondition;
    let fullParams = [...baseParams];

    // 月份筛选
    if (months && months.length > 0) {
        // 处理月份格式，数据库中月份字段存储的是6位数格式如202301
        const monthConditions = months.map(month => {
            // 如果是6位数格式 (如202301)，直接匹配
            if (String(month).length === 6) {
                return `月份 = ${month}`;
            } else {
                // 如果是1-2位数，需要构造完整的年月格式
                // 这里假设是当前年份，实际应用中可能需要更复杂的逻辑
                const currentYear = new Date().getFullYear();
                const fullMonth = `${currentYear}${String(month).padStart(2, '0')}`;
                return `月份 = ${fullMonth}`;
            }
        });

        if (monthConditions.length > 0) {
            fullWhereCondition += ` AND (${monthConditions.join(' OR ')})`;
        }
    }

    // 季度筛选 - 直接使用季度字段
    if (quarters && quarters.length > 0) {
        const quarterConditions = quarters.map(quarter => `季度 = '${quarter}'`);

        if (quarterConditions.length > 0) {
            fullWhereCondition += ` AND (${quarterConditions.join(' OR ')})`;
        }
    }

    console.log('基础WHERE条件:', baseWhereCondition);
    console.log('基础参数:', baseParams);
    console.log('完整WHERE条件:', fullWhereCondition);
    console.log('完整参数:', fullParams);

    // 获取产品分析数据，包含比较数据
    getSalesAnalysisData(fullWhereCondition, baseWhereCondition, baseParams, months, quarters, res);
});

// 获取月份明细数据API
app.post('/api/monthly-details', authenticateToken, (req, res) => {
    const { products, months, quarters, terminals, reps, regionProductLines, detailType, targetName } = req.body;

    console.log('=== 月份明细API调用 ===');
    console.log('用户:', req.user.username, '角色:', req.user.role);
    console.log('筛选条件:', { products, months, quarters, terminals, reps, regionProductLines });
    console.log('明细类型:', detailType, '目标名称:', targetName);

    // 构建基础WHERE条件
    let baseWhereCondition = '1=1';
    let baseParams = [];

    // 权限控制
    const isAdmin = req.user.role === 'admin' || req.user.username === 'admin';

    if (!isAdmin) {
        const userRepCode = req.user.rep_code || req.user.full_name || req.user.username;
        baseWhereCondition += ' AND 新REP = ?';
        baseParams.push(userRepCode);
    }

    // 根据明细类型添加特定筛选条件
    if (detailType === 'personnel' && targetName) {
        // 人员月份明细：筛选特定销售员
        baseWhereCondition += ' AND 新REP = ?';
        baseParams.push(targetName);
    } else if (detailType === 'product' && targetName) {
        // 产品月份明细：筛选特定产品
        baseWhereCondition += ' AND 产品 = ?';
        baseParams.push(targetName);
    }

    // 其他筛选条件
    // 当detailType === 'product'时，我们已经添加了特定产品的筛选条件，但仍需要应用其他产品筛选
    // 当detailType === 'personnel'时，我们已经添加了特定人员的筛选条件，但仍需要应用其他人员筛选
    if (products && products.length > 0 && detailType !== 'product') {
        const placeholders = products.map(() => '?').join(',');
        baseWhereCondition += ` AND 产品 IN (${placeholders})`;
        baseParams.push(...products);
    }

    if (reps && reps.length > 0 && detailType !== 'personnel') {
        const placeholders = reps.map(() => '?').join(',');
        baseWhereCondition += ` AND 新REP IN (${placeholders})`;
        baseParams.push(...reps);
    }

    if (terminals && terminals.length > 0) {
        const placeholders = terminals.map(() => '?').join(',');
        baseWhereCondition += ` AND 医院 IN (${placeholders})`;
        baseParams.push(...terminals);
    }

    if (regionProductLines && regionProductLines.length > 0) {
        const placeholders = regionProductLines.map(() => '?').join(',');
        baseWhereCondition += ` AND 地区产品线 IN (${placeholders})`;
        baseParams.push(...regionProductLines);
    }

    // 月份筛选
    if (months && months.length > 0) {
        const monthConditions = months.map(month => {
            if (String(month).length === 6) {
                return `月份 = ${month}`;
            } else {
                const currentYear = new Date().getFullYear();
                const fullMonth = `${currentYear}${String(month).padStart(2, '0')}`;
                return `月份 = ${fullMonth}`;
            }
        });

        if (monthConditions.length > 0) {
            baseWhereCondition += ` AND (${monthConditions.join(' OR ')})`;
        }
    }

    console.log('月份明细WHERE条件:', baseWhereCondition);
    console.log('月份明细参数:', baseParams);

    // 获取月份明细数据
    getMonthlyDetailsData(baseWhereCondition, baseParams, months, detailType, targetName, res);
});

// 动态分析API
app.post('/api/dynamic-analysis', authenticateToken, (req, res) => {
    console.log('=== 收到动态分析API请求 ===');
    console.log('请求时间:', new Date().toISOString());
    console.log('用户:', req.user ? req.user.username : '未知', '角色:', req.user ? req.user.role : '未知');

    const { products, months, quarters, terminals, reps, regionProductLines, dimensions } = req.body;

    console.log('=== 动态分析API调用 ===');
    console.log('筛选条件:', { products, months, quarters, terminals, reps, regionProductLines });
    console.log('分析维度:', dimensions);
    console.log('请求体完整内容:', JSON.stringify(req.body, null, 2));
    console.log('分析维度:', dimensions);

    if (!dimensions || dimensions.length === 0) {
        return res.json({
            success: false,
            error: '请选择分析维度'
        });
    }

    try {
        getDynamicAnalysisData(req, res, dimensions);
    } catch (error) {
        console.error('动态分析API错误:', error);
        res.status(500).json({
            success: false,
            error: '服务器内部错误'
        });
    }
});

// 月份同期对比柱状图数据API
app.post('/api/monthly-comparison-chart', authenticateToken, (req, res) => {
    const { products, months, quarters, terminals, reps, regionProductLines } = req.body;

    console.log('=== 月份同期对比柱状图API调用 ===');
    console.log('用户:', req.user.username, '角色:', req.user.role);
    console.log('筛选条件:', { products, months, quarters, terminals, reps, regionProductLines });

    // 如果没有月份筛选条件，返回空数据
    if (!months || months.length === 0) {
        return res.json({
            success: true,
            data: {
                chartData: [],
                shouldDisplay: false
            }
        });
    }

    // 构建基础WHERE条件（不包含时间筛选）
    let baseWhereCondition = '1=1';
    let baseParams = [];

    // 权限控制
    if (req.user.role !== 'admin') {
        // 普通用户只能查看自己的数据
        // 优先使用 rep_code，如果没有则使用 full_name，最后使用 username
        const userRepCode = req.user.rep_code || req.user.full_name || req.user.username;
        baseWhereCondition += ' AND 新REP = ?';
        baseParams.push(userRepCode);
    } else {
        // 管理员可以筛选指定的销售员
        if (reps && reps.length > 0) {
            const placeholders = reps.map(() => '?').join(',');
            baseWhereCondition += ` AND 新REP IN (${placeholders})`;
            baseParams.push(...reps);
        }
    }

    // 产品筛选
    if (products && products.length > 0) {
        const placeholders = products.map(() => '?').join(',');
        baseWhereCondition += ` AND 产品 IN (${placeholders})`;
        baseParams.push(...products);
    }

    // 医院筛选
    if (terminals && terminals.length > 0) {
        const placeholders = terminals.map(() => '?').join(',');
        baseWhereCondition += ` AND 医院 IN (${placeholders})`;
        baseParams.push(...terminals);
    }

    // 地区产品线筛选
    if (regionProductLines && regionProductLines.length > 0) {
        const placeholders = regionProductLines.map(() => '?').join(',');
        baseWhereCondition += ` AND 地区产品线 IN (${placeholders})`;
        baseParams.push(...regionProductLines);
    }

    // 获取月份同期对比数据
    getMonthlyComparisonData(baseWhereCondition, baseParams, months, res);
});

// 月份环比增长柱状图数据API
app.post('/api/monthly-mom-chart', authenticateToken, (req, res) => {
    const { products, months, quarters, terminals, reps, regionProductLines } = req.body;

    console.log('=== 月份环比增长柱状图API调用 ===');
    console.log('用户:', req.user.username, '角色:', req.user.role);
    console.log('筛选条件:', { products, months, quarters, terminals, reps, regionProductLines });

    // 如果没有月份筛选条件，返回空数据
    if (!months || months.length === 0) {
        return res.json({
            success: true,
            data: {
                chartData: [],
                shouldDisplay: false
            }
        });
    }

    // 构建基础WHERE条件（不包含时间筛选）
    let baseWhereCondition = '1=1';
    let baseParams = [];

    // 权限控制
    if (req.user.role !== 'admin') {
        // 普通用户只能查看自己的数据
        // 优先使用 rep_code，如果没有则使用 full_name，最后使用 username
        const userRepCode = req.user.rep_code || req.user.full_name || req.user.username;
        baseWhereCondition += ' AND 新REP = ?';
        baseParams.push(userRepCode);
    } else {
        // 管理员可以筛选指定的销售员
        if (reps && reps.length > 0) {
            const placeholders = reps.map(() => '?').join(',');
            baseWhereCondition += ` AND 新REP IN (${placeholders})`;
            baseParams.push(...reps);
        }
    }

    // 产品筛选
    if (products && products.length > 0) {
        const placeholders = products.map(() => '?').join(',');
        baseWhereCondition += ` AND 产品 IN (${placeholders})`;
        baseParams.push(...products);
    }

    // 医院筛选
    if (terminals && terminals.length > 0) {
        const placeholders = terminals.map(() => '?').join(',');
        baseWhereCondition += ` AND 医院 IN (${placeholders})`;
        baseParams.push(...terminals);
    }

    // 地区产品线筛选
    if (regionProductLines && regionProductLines.length > 0) {
        const placeholders = regionProductLines.map(() => '?').join(',');
        baseWhereCondition += ` AND 地区产品线 IN (${placeholders})`;
        baseParams.push(...regionProductLines);
    }

    // 获取月份环比增长数据
    getMonthlyMomData(baseWhereCondition, baseParams, months, res);
});

// 根路由 - 返回登录页面
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'login.html'));
});

// 获取销售分析数据
function getSalesAnalysisData(fullWhereCondition, baseWhereCondition, baseParams, selectedMonths, selectedQuarters, res) {
    console.log('=== 开始获取销售分析数据 ===');
    console.log('完整WHERE条件:', fullWhereCondition);
    console.log('基础WHERE条件:', baseWhereCondition);
    console.log('基础参数:', baseParams);
    console.log('选择的月份:', selectedMonths);
    console.log('选择的季度:', selectedQuarters);

    // 1. 获取产品分析数据 - 按产品名称分组以匹配筛选器
    const productQuery = `
        SELECT
            MIN(产品编码concat) as productCode,
            产品 as productName,
            SUM(CAST(销量盒折算后 as REAL)) as actualQuantity,
            SUM(CAST(销售金额折算后 as REAL)) as actualAmount,
            SUM(CAST(指标金额 as REAL)) as targetAmount,
            COUNT(*) as recordCount
        FROM salestable
        WHERE ${fullWhereCondition} AND 产品 IS NOT NULL AND 产品编码concat IS NOT NULL
        GROUP BY 产品
        ORDER BY actualAmount DESC
    `;

    console.log('执行查询:', productQuery);

    // 为产品查询构建完整的参数数组
    let productParams = [...baseParams];

    db.all(productQuery, productParams, (err, productRows) => {
        if (err) {
            console.error('查询产品数据错误:', err);
            return res.status(500).json({ success: false, error: '查询产品数据失败: ' + err.message });
        }

        console.log('产品数据行数:', productRows.length);
        console.log('产品数据样本:', productRows.slice(0, 3));

        if (productRows.length === 0) {
            // 如果没有产品数据，直接返回空结果
            console.log('没有产品数据，返回空结果');
            return res.json({
                success: true,
                data: {
                    products: [],
                    summary: {
                        totalActual: 0,
                        totalTarget: 0,
                        samePeriodComparison: { current: 0, previous: 0 },
                        sequentialComparison: { current: 0, previous: 0 }
                    }
                }
            });
        }

        // 为每个产品计算比较数据
        let completedProducts = 0;
        const productsWithComparison = [];

        productRows.forEach((row, index) => {
            // 为比较数据查询构建不包含时间条件的WHERE条件，包含所有筛选条件
            const productBaseWhereCondition = `${baseWhereCondition} AND 产品 = ?`;
            const productBaseParams = [...baseParams, row.productName];

            console.log(`=== 计算产品 ${row.productName} 的比较数据 ===`);
            console.log('产品WHERE条件:', productBaseWhereCondition);
            console.log('产品参数:', productBaseParams);
            console.log('选择的月份:', selectedMonths);

            // 获取产品的比较数据
            getSalesAnalysisComparisonData(productBaseWhereCondition, productBaseParams, selectedMonths, selectedQuarters, (err, comparisonData) => {
                if (err) {
                    console.error(`获取产品 ${row.productName} 比较数据错误:`, err);
                    // 使用默认值
                    comparisonData = {
                        samePeriod: { current: row.actualAmount || 0, previous: 0 },
                        sequential: { current: row.actualAmount || 0, previous: 0 }
                    };
                }

                // 计算增长率（使用新的算法）
                const samePeriodGrowth = calculateGrowthRate(
                    comparisonData.samePeriod.current,
                    comparisonData.samePeriod.previous
                );
                const sequentialGrowth = calculateGrowthRate(
                    comparisonData.sequential.current,
                    comparisonData.sequential.previous
                );

                productsWithComparison.push({
                    name: row.productName || '未知产品',
                    productCode: row.productCode,
                    actual: row.actualAmount || 0,
                    target: row.targetAmount || 0,
                    lastYearSales: comparisonData.samePeriod.previous || 0,
                    lastPeriodSales: comparisonData.sequential.previous || 0,
                    actualQuantity: row.actualQuantity || 0,
                    samePeriodGrowth: samePeriodGrowth,
                    sequentialGrowth: sequentialGrowth
                });

                completedProducts++;

                // 当所有产品的比较数据都计算完成后，继续处理
                if (completedProducts === productRows.length) {
                    console.log('所有产品比较数据计算完成');
                    continueWithSummaryData();
                }
            });
        });

        function continueWithSummaryData() {
            console.log('产品比较数据样本:', productsWithComparison.slice(0, 3));

            // 获取总体比较数据（使用不包含时间条件的基础WHERE条件，包含所有筛选条件）
            getSalesAnalysisComparisonData(baseWhereCondition, baseParams, selectedMonths, selectedQuarters, (err, comparisonData) => {
                if (err) {
                    console.error('获取总体比较数据错误:', err);
                    // 如果比较数据获取失败，使用默认值
                    comparisonData = {
                        samePeriod: { current: 0, previous: 0 },
                        sequential: { current: 0, previous: 0 },
                        yearOverYear: { current: 0, previous: 0 }
                    };
                }

                console.log('总体比较数据:', comparisonData);

                // 计算总计，包含比较数据
                const summary = {
                    totalActual: productsWithComparison.reduce((sum, p) => sum + p.actual, 0),
                    totalTarget: productsWithComparison.reduce((sum, p) => sum + p.target, 0),
                    totalLastYear: comparisonData.samePeriod?.previous || 0,
                    totalLastPeriod: comparisonData.sequential?.previous || 0,
                    // 添加比较数据
                    samePeriodComparison: comparisonData.samePeriod,
                    sequentialComparison: comparisonData.sequential,
                    yearOverYearComparison: comparisonData.yearOverYear
                };

            // 获取人员数据
            const personnelQuery = `
                SELECT
                    新REP as name,
                    SUM(CAST(销售金额折算后 AS REAL)) as actual,
                    SUM(CAST(指标金额 AS REAL)) as target,
                    COUNT(DISTINCT 产品) as productCount
                FROM salestable
                WHERE ${fullWhereCondition} AND 新REP IS NOT NULL AND 新REP != ''
                GROUP BY 新REP
                ORDER BY actual DESC
            `;

            console.log('执行人员查询:', personnelQuery);

            db.all(personnelQuery, productParams, (err, personnelRows) => {
                if (err) {
                    console.error('查询人员数据错误:', err);
                    // 如果人员数据查询失败，仍然返回产品数据
                    personnelRows = [];
                }

                console.log('人员数据行数:', personnelRows.length);
                console.log('人员数据样本:', personnelRows.slice(0, 3));

                // 为每个人员计算比较数据
                let completedPersonnel = 0;
                const personnelWithComparison = [];

                if (personnelRows.length === 0) {
                    // 如果没有人员数据，直接返回结果
                    const result = {
                        success: true,
                        data: {
                            products: productsWithComparison,
                            personnel: [],
                            summary: summary
                        }
                    };
                    return res.json(result);
                }

                personnelRows.forEach((row, index) => {
                    // 为比较数据查询构建不包含时间条件的WHERE条件，保持与当期查询相同的筛选条件
                    const personnelBaseWhereCondition = baseWhereCondition + ` AND 新REP = ?`;
                    const personnelBaseParams = [...baseParams, row.name];

                    // 获取人员的比较数据
                    getSalesAnalysisComparisonData(personnelBaseWhereCondition, personnelBaseParams, selectedMonths, selectedQuarters, (err, comparisonData) => {
                        if (err) {
                            console.error(`获取人员 ${row.name} 比较数据错误:`, err);
                            // 使用默认值
                            comparisonData = {
                                samePeriod: { current: row.actual || 0, previous: 0 },
                                sequential: { current: row.actual || 0, previous: 0 }
                            };
                        }

                        // 计算增长率（使用新的算法）
                        const samePeriodGrowth = calculateGrowthRate(
                            comparisonData.samePeriod.current,
                            comparisonData.samePeriod.previous
                        );
                        const sequentialGrowth = calculateGrowthRate(
                            comparisonData.sequential.current,
                            comparisonData.sequential.previous
                        );

                        personnelWithComparison.push({
                            name: row.name || '未知人员',
                            actual: row.actual || 0,
                            target: row.target || 0,
                            lastYearSales: comparisonData.samePeriod.previous || 0,
                            lastPeriodSales: comparisonData.sequential.previous || 0,
                            productCount: row.productCount || 0,
                            samePeriodGrowth: samePeriodGrowth,
                            sequentialGrowth: sequentialGrowth
                        });

                        completedPersonnel++;

                        // 当所有人员的比较数据都计算完成后，返回结果
                        if (completedPersonnel === personnelRows.length) {
                            console.log('所有人员比较数据计算完成');

                            const result = {
                                success: true,
                                data: {
                                    products: productsWithComparison,
                                    personnel: personnelWithComparison,
                                    summary: summary
                                }
                            };

                            console.log('返回分析数据:', {
                                productsCount: productsWithComparison.length,
                                personnelCount: personnelWithComparison.length,
                                totalActual: summary.totalActual,
                                totalTarget: summary.totalTarget,
                                totalLastYear: summary.totalLastYear,
                                totalLastPeriod: summary.totalLastPeriod,
                                samePeriodComparison: summary.samePeriodComparison,
                                sequentialComparison: summary.sequentialComparison
                            });

                            res.json(result);
                        }
                    });
                });
            });
            });
        }
    });
}



// 启动服务器
app.listen(PORT, '0.0.0.0', () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
    console.log('默认管理员账号: admin / admin123');

    // 获取本机IP地址并显示局域网访问地址
    const networkInterfaces = os.networkInterfaces();
    console.log('\n局域网访问地址:');

    Object.keys(networkInterfaces).forEach((interfaceName) => {
        const interfaces = networkInterfaces[interfaceName];
        interfaces.forEach((iface) => {
            if (iface.family === 'IPv4' && !iface.internal) {
                console.log(`  http://${iface.address}:${PORT}`);
            }
        });
    });
});

// 获取产品环比增长数据
function getMonthlyMomData(baseWhereCondition, baseParams, selectedMonths, res) {
    console.log('=== 获取产品环比增长数据 ===');
    console.log('基础WHERE条件:', baseWhereCondition);
    console.log('选择的月份:', selectedMonths);

    // 计算环比月份：往前推相同数量的月份
    const monthCount = selectedMonths.length;
    const currentYear = Math.floor(selectedMonths[0] / 100);
    const currentMonth = selectedMonths[0] % 100;
    
    // 计算环比年月
    let compYear = currentYear;
    let compMonth = currentMonth - monthCount;
    
    while (compMonth <= 0) {
        compMonth += 12;
        compYear -= 1;
    }

    // 构建环比月份数组
    const comparisonMonths = [];
    for (let i = 0; i < monthCount; i++) {
        const compMonthCode = compYear * 100 + compMonth + i;
        if (compMonth + i > 12) {
            comparisonMonths.push((compYear + 1) * 100 + (compMonth + i - 12));
        } else {
            comparisonMonths.push(compMonthCode);
        }
    }

    console.log('环比月份:', comparisonMonths);

    // 构建当期月份筛选条件
    const currentMonthConditions = selectedMonths.map(month => `月份 = ?`).join(' OR ');
    const currentWhereCondition = `${baseWhereCondition} AND (${currentMonthConditions})`;
    const currentParams = [...baseParams, ...selectedMonths];

    // 构建环比月份筛选条件
    const comparisonMonthConditions = comparisonMonths.map(month => `月份 = ?`).join(' OR ');
    const comparisonWhereCondition = `${baseWhereCondition} AND (${comparisonMonthConditions})`;
    const comparisonParams = [...baseParams, ...comparisonMonths];

    // 查询当期按产品汇总的数据
    const currentQuery = `
        SELECT 
            产品 as product,
            SUM(CAST(销售金额折算后 AS REAL)) as totalSales
        FROM salestable
        WHERE ${currentWhereCondition} AND 产品 IS NOT NULL
        GROUP BY 产品
        ORDER BY totalSales DESC
    `;

    // 查询环比按产品汇总的数据
    const comparisonQuery = `
        SELECT 
            产品 as product,
            SUM(CAST(销售金额折算后 AS REAL)) as totalSales
        FROM salestable
        WHERE ${comparisonWhereCondition} AND 产品 IS NOT NULL
        GROUP BY 产品
        ORDER BY totalSales DESC
    `;

    // 执行当期数据查询
    db.all(currentQuery, currentParams, (err, currentResults) => {
        if (err) {
            console.error('查询当期产品数据失败:', err);
            return res.status(500).json({ success: false, error: '查询当期产品数据失败' });
        }

        // 执行环比数据查询
        db.all(comparisonQuery, comparisonParams, (err, comparisonResults) => {
            if (err) {
                console.error('查询环比产品数据失败:', err);
                return res.status(500).json({ success: false, error: '查询环比产品数据失败' });
            }

            // 创建环比数据的映射
            const comparisonMap = {};
            comparisonResults.forEach(item => {
                comparisonMap[item.product] = item.totalSales;
            });

            // 合并当期和环比数据
            const chartData = currentResults.map(item => {
                const currentValue = item.totalSales || 0;
                const compValue = comparisonMap[item.product] || 0;

                // 计算环比增长率
                let growthRate = 0;
                if (compValue > 0) {
                    growthRate = ((currentValue - compValue) / compValue * 100);
                }

                console.log(`${item.product}: 当期=${currentValue}, 环比=${compValue}, 增长率=${growthRate.toFixed(1)}%`);

                return {
                    product: item.product,
                    currentMonth: currentValue,
                    previousMonth: compValue,
                    growthRate: growthRate.toFixed(1)
                };
            });

            console.log('产品环比增长数据查询完成:', chartData);
            res.json({
                success: true,
                data: {
                    chartData: chartData,
                    shouldDisplay: chartData.length > 0
                }
            });
        });
    });
}

// 获取产品同期对比数据
function getMonthlyComparisonData(baseWhereCondition, baseParams, selectedMonths, res) {
    console.log('=== 获取产品同期对比数据 ===');
    console.log('基础WHERE条件:', baseWhereCondition);
    console.log('选择的月份:', selectedMonths);

    // 构建月份筛选条件
    const monthConditions = selectedMonths.map(month => `月份 = ?`).join(' OR ');
    const monthWhereCondition = `${baseWhereCondition} AND (${monthConditions})`;
    const monthParams = [...baseParams, ...selectedMonths];

    // 获取当期年份（从选择的月份中获取年份）
    const currentYear = Math.floor(selectedMonths[0] / 100);
    const lastYear = currentYear - 1;

    // 构建去年同期的月份
    const lastYearMonths = selectedMonths.map(month => {
        const monthNum = month % 100;
        return lastYear * 100 + monthNum;
    });

    const lastYearConditions = lastYearMonths.map(month => `月份 = ?`).join(' OR ');
    const lastYearWhereCondition = `${baseWhereCondition} AND (${lastYearConditions})`;
    const lastYearParams = [...baseParams, ...lastYearMonths];

    // 查询当期按产品汇总的数据
    const currentQuery = `
        SELECT 
            产品 as product,
            SUM(CAST(销售金额折算后 AS REAL)) as totalSales
        FROM salestable
        WHERE ${monthWhereCondition} AND 产品 IS NOT NULL
        GROUP BY 产品
        ORDER BY totalSales DESC
    `;

    // 查询去年同期按产品汇总的数据
    const lastYearQuery = `
        SELECT 
            产品 as product,
            SUM(CAST(销售金额折算后 AS REAL)) as totalSales
        FROM salestable
        WHERE ${lastYearWhereCondition} AND 产品 IS NOT NULL
        GROUP BY 产品
        ORDER BY totalSales DESC
    `;

    // 执行当期数据查询
    db.all(currentQuery, monthParams, (err, currentResults) => {
        if (err) {
            console.error('查询当期产品数据错误:', err);
            return res.status(500).json({ success: false, error: '查询当期产品数据失败' });
        }

        // 执行去年同期数据查询
        db.all(lastYearQuery, lastYearParams, (err, lastYearResults) => {
            if (err) {
                console.error('查询去年同期产品数据错误:', err);
                return res.status(500).json({ success: false, error: '查询去年同期产品数据失败' });
            }

            // 创建去年数据的映射
            const lastYearMap = {};
            lastYearResults.forEach(item => {
                lastYearMap[item.product] = item.totalSales;
            });

            // 合并当期和去年数据
            const chartData = currentResults.map(item => {
                const currentSales = item.totalSales || 0;
                const lastYearSales = lastYearMap[item.product] || 0;

                // 计算增长率（使用新的算法）
                const growthRateText = calculateGrowthRate(currentSales, lastYearSales);
                // 为了保持图表兼容性，提取数值部分
                let growthRate = 0;
                if (growthRateText.includes('%')) {
                    const match = growthRateText.match(/([+-]?\d+\.?\d*)/);
                    growthRate = match ? parseFloat(match[1]) : 0;
                } else if (growthRateText.includes('改善')) {
                    const match = growthRateText.match(/改善(\d+)/);
                    growthRate = match ? parseFloat(match[1]) : 0;
                }

                return {
                    product: item.product,
                    currentYear: currentSales,
                    lastYear: lastYearSales,
                    growthRate: growthRate.toFixed(1),
                    growthRateText: growthRateText
                };
            });

            console.log('产品同期对比数据:', chartData);
            
            res.json({
                success: true,
                data: {
                    chartData: chartData,
                    shouldDisplay: chartData.length > 0
                }
            });
        });
    });
}

// 销售指标对比图表数据API
app.post('/api/sales-target-comparison-chart', authenticateToken, (req, res) => {
    const { products, months, quarters, terminals, reps, regionProductLines } = req.body;

    console.log('=== 销售指标对比图表API调用 ===');
    console.log('用户:', req.user.username, '角色:', req.user.role);
    console.log('筛选条件:', { products, months, quarters, terminals, reps, regionProductLines });

    // 如果没有月份筛选条件，不加载数据
    let selectedMonths = months;
    if (!months || months.length === 0) {
        console.log('没有选择月份筛选条件，不加载销售指标对比数据');
        return res.json({
            success: true,
            data: {
                chartData: [],
                shouldDisplay: false
            }
        });
    }

    // 处理数据的函数
    function processSalesTargetData() {

    // 构建基础WHERE条件（不包含时间筛选）
    let baseWhereCondition = '1=1';
    let baseParams = [];

    // 权限控制
    if (req.user.role !== 'admin') {
        // 普通用户只能查看自己的数据
        // 优先使用 rep_code，如果没有则使用 full_name，最后使用 username
        const userRepCode = req.user.rep_code || req.user.full_name || req.user.username;
        baseWhereCondition += ' AND 新REP = ?';
        baseParams.push(userRepCode);
    } else {
        // 管理员可以筛选指定的销售员
        if (reps && reps.length > 0) {
            const placeholders = reps.map(() => '?').join(',');
            baseWhereCondition += ` AND 新REP IN (${placeholders})`;
            baseParams.push(...reps);
        }
    }

    // 添加产品筛选
    if (products && products.length > 0) {
        const productPlaceholders = products.map(() => '?').join(',');
        baseWhereCondition += ` AND 产品 IN (${productPlaceholders})`;
        baseParams.push(...products);
    }

    // 添加医院筛选
    if (terminals && terminals.length > 0) {
        const terminalPlaceholders = terminals.map(() => '?').join(',');
        baseWhereCondition += ` AND 医院 IN (${terminalPlaceholders})`;
        baseParams.push(...terminals);
    }

    // 添加地区产品线筛选
    if (regionProductLines && regionProductLines.length > 0) {
        const regionProductLinePlaceholders = regionProductLines.map(() => '?').join(',');
        baseWhereCondition += ` AND 地区产品线 IN (${regionProductLinePlaceholders})`;
        baseParams.push(...regionProductLines);
    }

    console.log('基础WHERE条件:', baseWhereCondition);
    console.log('基础参数:', baseParams);

    // 获取销售指标对比数据
    getSalesTargetComparisonData(baseWhereCondition, baseParams, selectedMonths, res);
    }

    // 如果有月份筛选条件，直接处理
    if (months && months.length > 0) {
        processSalesTargetData();
    }
});

// 获取销售指标对比数据
function getSalesTargetComparisonData(baseWhereCondition, baseParams, selectedMonths, res) {
    console.log('=== 获取销售指标对比数据 ===');
    console.log('基础WHERE条件:', baseWhereCondition);
    console.log('选择的月份:', selectedMonths);

    const chartData = [];
    let completedMonths = 0;

    selectedMonths.forEach(month => {
        console.log(`处理月份: ${month}`);

        // 解析月份
        const monthStr = String(month);
        const year = parseInt(monthStr.substring(0, 4));
        const monthNum = parseInt(monthStr.substring(4, 6));
        const monthDisplay = `${year}年${String(monthNum).padStart(2, '0')}月`;

        // 查询该月份的销售和指标数据
        const query = `
            SELECT
                SUM(CAST(销售金额折算后 AS REAL)) as salesAmount,
                SUM(CAST(指标金额 AS REAL)) as targetAmount,
                SUM(CAST(销量盒折算后 AS REAL)) as salesQuantity,
                SUM(CAST(指标盒折算后 AS REAL)) as targetBoxConverted,
                COUNT(*) as recordCount
            FROM salestable
            WHERE ${baseWhereCondition} AND 月份 = ?
        `;

        const params = [...baseParams, monthStr];

        db.get(query, params, (err, result) => {
            if (err) {
                console.error(`查询${month}销售指标数据错误:`, err);
                // 只在第一次错误时发送响应
                if (!res.headersSent) {
                    return res.status(500).json({ success: false, error: '查询销售指标数据失败' });
                }
                return;
            }

            const salesAmount = result?.salesAmount || 0;
            const targetAmount = result?.targetAmount || 0;
            const salesQuantity = result?.salesQuantity || 0;
            const targetBoxConverted = result?.targetBoxConverted || 0;

            // 计算QP值 (假设QP值 = 销售金额折算后 / 指标金额)
            let qpValue = 0;
            if (targetAmount > 0) {
                qpValue = salesAmount / targetAmount;
            }

            // 格式化QP值为百分比
            const qpValuePercent = (qpValue * 100).toFixed(1) + '%';

            console.log(`${monthDisplay}: 销售金额=${salesAmount}, 指标金额=${targetAmount}, 销量=${salesQuantity}, 指标盒折算=${targetBoxConverted}, QP=${qpValuePercent}`);

            chartData.push({
                period: monthDisplay,
                month: monthDisplay,
                salesAmount: salesAmount,
                targetAmount: targetAmount,
                salesQuantity: salesQuantity,
                targetBoxConverted: targetBoxConverted,
                qpValue: qpValuePercent
            });

            completedMonths++;
            if (completedMonths === selectedMonths.length) {
                // 按月份排序
                chartData.sort((a, b) => {
                    const aMonth = a.period.replace(/年|月/g, '');
                    const bMonth = b.period.replace(/年|月/g, '');
                    return aMonth.localeCompare(bMonth);
                });

                console.log('销售指标对比数据查询完成:', chartData);
                res.json({
                    success: true,
                    data: {
                        chartData: chartData,
                        shouldDisplay: true  // 总是显示，即使某些月份没有数据
                    }
                });
            }
        });
    });
}

// 清空冗余数据API
app.post('/api/clean-redundant-data', authenticateToken, (req, res) => {
    console.log('=== 清空冗余数据API调用 ===');
    console.log('用户:', req.user.username, '角色:', req.user.role);

    // 只有管理员可以执行此操作
    if (req.user.role !== 'admin') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以清空冗余数据'
        });
    }

    // 构建删除条件：销量盒折算后、销售金额折算后、指标盒折算后、指标金额都为0
    const deleteQuery = `
        DELETE FROM salestable
        WHERE (销量盒折算后 = 0 OR 销量盒折算后 IS NULL)
          AND (销售金额折算后 = 0 OR 销售金额折算后 IS NULL)
          AND (指标盒折算后 = 0 OR 指标盒折算后 IS NULL)
          AND (指标金额 = 0 OR 指标金额 IS NULL)
    `;

    console.log('执行删除查询:', deleteQuery);

    db.run(deleteQuery, function(err) {
        if (err) {
            console.error('删除冗余数据失败:', err);
            return res.status(500).json({
                success: false,
                message: '删除冗余数据失败: ' + err.message
            });
        }

        const deletedCount = this.changes;
        console.log(`成功删除 ${deletedCount} 条冗余数据`);

        res.json({
            success: true,
            message: '冗余数据清理完成',
            deletedCount: deletedCount
        });
    });
});

// 获取月份明细数据
function getMonthlyDetailsData(whereCondition, params, months, detailType, targetName, res) {
    console.log('=== 获取月份明细数据 ===');
    console.log('WHERE条件:', whereCondition);
    console.log('参数:', params);
    console.log('月份:', months);
    console.log('明细类型:', detailType);
    console.log('目标名称:', targetName);

    // 构建按月份分组的查询
    const query = `
        SELECT
            月份,
            SUM(CAST(销售金额折算后 AS REAL)) as actual,
            SUM(CAST(指标金额 AS REAL)) as target
        FROM salestable
        WHERE ${whereCondition}
        GROUP BY 月份
        ORDER BY 月份
    `;

    console.log('月份明细查询SQL:', query);

    db.all(query, params, (err, rows) => {
        if (err) {
            console.error('获取月份明细数据失败:', err);
            return res.status(500).json({
                success: false,
                error: '获取月份明细数据失败: ' + err.message
            });
        }

        console.log('月份明细原始数据:', rows);

        // 为每个月份计算同期和环比数据
        let completedMonths = 0;
        const monthlyDetails = [];
        const totalMonths = rows.length;

        if (totalMonths === 0) {
            return res.json({
                success: true,
                data: {
                    monthlyDetails: [],
                    detailType: detailType,
                    targetName: targetName
                }
            });
        }

        rows.forEach((row, index) => {
            const actual = parseFloat(row.actual) || 0;
            const target = parseFloat(row.target) || 0;
            const monthStr = String(row.月份);

            // 格式化月份显示
            let monthDisplay = monthStr;
            if (monthStr.length === 6) {
                const year = monthStr.substring(0, 4);
                const month = monthStr.substring(4, 6);
                monthDisplay = `${year}年${parseInt(month)}月`;
            }

            // 为单个月份计算比较数据，使用与产品销售完成数据分析表相同的逻辑
            getSalesAnalysisComparisonData(whereCondition, params, [row.月份], null, (err, comparisonData) => {
                if (err) {
                    console.error(`获取月份 ${monthStr} 比较数据错误:`, err);
                    comparisonData = {
                        samePeriod: { current: actual, previous: 0 },
                        sequential: { current: actual, previous: 0 }
                    };
                }

                // 计算增长率，使用与产品销售完成数据分析表相同的算法
                const yearOverYearGrowth = calculateGrowthRate(
                    comparisonData.samePeriod.current,
                    comparisonData.samePeriod.previous
                );
                const periodOverPeriodGrowth = calculateGrowthRate(
                    comparisonData.sequential.current,
                    comparisonData.sequential.previous
                );

                monthlyDetails.push({
                    month: monthDisplay,
                    period: monthDisplay,
                    actual: actual,
                    target: target,
                    lastYearSales: comparisonData.samePeriod.previous || 0,
                    lastPeriodSales: comparisonData.sequential.previous || 0,
                    yearOverYearGrowth: yearOverYearGrowth,  // 保持字符串格式，与产品销售完成数据分析表一致
                    periodOverPeriodGrowth: periodOverPeriodGrowth  // 保持字符串格式，与产品销售完成数据分析表一致
                });

                completedMonths++;
                if (completedMonths === totalMonths) {
                    // 按月份排序
                    monthlyDetails.sort((a, b) => {
                        const aMonth = a.month.replace(/年|月/g, '').replace(/(\d{4})(\d{1,2})/, '$1$2');
                        const bMonth = b.month.replace(/年|月/g, '').replace(/(\d{4})(\d{1,2})/, '$1$2');
                        return aMonth.localeCompare(bMonth);
                    });

                    console.log('处理后的月份明细数据:', monthlyDetails);

                    res.json({
                        success: true,
                        data: {
                            monthlyDetails: monthlyDetails,
                            detailType: detailType,
                            targetName: targetName
                        }
                    });
                }
            });
        });
    });
}

// 获取动态分析数据
function getDynamicAnalysisData(req, res, dimensions) {
    const { products, months, quarters, terminals, reps, regionProductLines } = req.body;

    // 设置全局变量供对比数据查询使用
    global.currentAnalysisMonths = months || [];

    // 构建基础WHERE条件
    let baseWhereCondition = '1=1';
    let baseParams = [];

    // 权限控制
    const isAdmin = req.user.role === 'admin' || req.user.username === 'admin';

    if (!isAdmin) {
        const userRepCode = req.user.rep_code || req.user.full_name || req.user.username;
        baseWhereCondition += ' AND 新REP = ?';
        baseParams.push(userRepCode);
    } else {
        if (reps && reps.length > 0) {
            const placeholders = reps.map(() => '?').join(',');
            baseWhereCondition += ` AND 新REP IN (${placeholders})`;
            baseParams.push(...reps);
        }
    }

    // 其他筛选条件
    if (products && products.length > 0) {
        const placeholders = products.map(() => '?').join(',');
        baseWhereCondition += ` AND 产品 IN (${placeholders})`;
        baseParams.push(...products);
    }

    if (terminals && terminals.length > 0) {
        const placeholders = terminals.map(() => '?').join(',');
        baseWhereCondition += ` AND 医院 IN (${placeholders})`;
        baseParams.push(...terminals);
    }

    if (regionProductLines && regionProductLines.length > 0) {
        const placeholders = regionProductLines.map(() => '?').join(',');
        baseWhereCondition += ` AND 地区产品线 IN (${placeholders})`;
        baseParams.push(...regionProductLines);
    }

    // 月份筛选
    if (months && months.length > 0) {
        const monthConditions = months.map(month => {
            if (String(month).length === 6) {
                return `月份 = ${month}`;
            } else {
                const currentYear = new Date().getFullYear();
                const fullMonth = `${currentYear}${String(month).padStart(2, '0')}`;
                return `月份 = ${fullMonth}`;
            }
        });

        if (monthConditions.length > 0) {
            baseWhereCondition += ` AND (${monthConditions.join(' OR ')})`;
        }
    }

    // 构建SELECT和GROUP BY子句
    let selectFields = [];
    let groupByFields = [];

    dimensions.forEach(dimension => {
        switch(dimension) {
            case 'product':
                selectFields.push('产品 as product');
                groupByFields.push('产品');
                break;
            case 'personnel':
                selectFields.push('新REP as personnel');
                groupByFields.push('新REP');
                break;
            case 'month':
                selectFields.push('月份 as month');
                groupByFields.push('月份');
                break;
            case 'terminal':
                selectFields.push('医院 as terminal');
                groupByFields.push('医院');
                break;
        }
    });

    // 添加聚合字段
    selectFields.push(
        'SUM(CAST(销售金额折算后 AS REAL)) as actual',
        'SUM(CAST(指标金额 AS REAL)) as target'
    );

    const selectClause = selectFields.join(', ');
    const groupByClause = groupByFields.join(', ');

    // 构建排序子句 - 按维度层级排序
    let orderByFields = [];
    dimensions.forEach(dimension => {
        switch(dimension) {
            case 'product':
                orderByFields.push('产品');
                break;
            case 'personnel':
                orderByFields.push('新REP');
                break;
            case 'month':
                orderByFields.push('月份');
                break;
            case 'terminal':
                orderByFields.push('医院');
                break;
        }
    });
    orderByFields.push('actual DESC'); // 最后按销售额降序

    const sql = `
        SELECT ${selectClause}
        FROM salestable
        WHERE ${baseWhereCondition}
        GROUP BY ${groupByClause}
        ORDER BY ${orderByFields.join(', ')}
    `;

    console.log('动态分析SQL:', sql);
    console.log('动态分析参数:', baseParams);

    db.all(sql, baseParams, (err, rows) => {
        if (err) {
            console.error('动态分析查询错误:', err);
            return res.status(500).json({
                success: false,
                error: '数据库查询失败'
            });
        }

        console.log('动态分析查询结果行数:', rows.length);

        // 为每行数据获取同期和上期数据，然后计算增长率
        let completedRows = 0;
        const totalRows = rows.length;
        const processedRows = [];

        if (totalRows === 0) {
            return res.json({
                success: true,
                data: []
            });
        }

        rows.forEach((row, index) => {
            // 构建用于比较查询的WHERE条件（不包含月份条件）
            // 因为比较查询需要使用不同的月份条件
            let comparisonWhereCondition = baseWhereCondition;
            let comparisonParams = [...baseParams];

            // 从baseWhereCondition中移除月份条件
            if (months && months.length > 0) {
                // 移除月份条件部分 - 处理单个月份的情况
                if (months.length === 1) {
                    const monthPattern = ` AND \\(月份 = ${months[0]}\\)`;
                    comparisonWhereCondition = comparisonWhereCondition.replace(new RegExp(monthPattern), '');
                } else {
                    // 处理多个月份的情况
                    const monthConditions = months.map(month => `月份 = ${month}`).join(' OR ');
                    const monthPattern = ` AND \\(${monthConditions}\\)`;
                    comparisonWhereCondition = comparisonWhereCondition.replace(new RegExp(monthPattern), '');
                }

                // 移除对应的月份参数
                comparisonParams = comparisonParams.slice(0, comparisonParams.length - months.length);

                console.log('移除月份条件后的WHERE:', comparisonWhereCondition);
                console.log('移除月份参数后的params:', comparisonParams);
            }

            // 根据维度添加具体的筛选条件
            dimensions.forEach(dimension => {
                switch(dimension) {
                    case 'product':
                        if (row.product) {
                            comparisonWhereCondition += ' AND 产品 = ?';
                            comparisonParams.push(row.product);
                        }
                        break;
                    case 'personnel':
                        if (row.personnel) {
                            comparisonWhereCondition += ' AND 新REP = ?';
                            comparisonParams.push(row.personnel);
                        }
                        break;
                    case 'terminal':
                        if (row.terminal) {
                            comparisonWhereCondition += ' AND 医院 = ?';
                            comparisonParams.push(row.terminal);
                        }
                        break;
                    case 'month':
                        // 对于月份维度，不在这里添加月份条件
                        // 因为比较查询需要使用选择的所有月份进行比较
                        // 月份条件由比较函数自己处理
                        break;
                }
            });

            // 使用与产品销售分析表完全相同的比较数据计算逻辑
            console.log(`=== 处理第${index + 1}行数据 ===`);
            console.log('行数据:', row);
            console.log('比较查询WHERE条件:', comparisonWhereCondition);
            console.log('比较查询参数:', comparisonParams);
            console.log('选择的月份:', months);
            console.log('包含的维度:', dimensions);

            // 判断是否包含月份维度
            const hasMonthDimension = dimensions.includes('month');
            let comparisonMonths = months;

            if (hasMonthDimension && row.month) {
                // 如果包含月份维度，只对当前行的月份进行比较
                comparisonMonths = [row.month];
                console.log('月份维度模式 - 当前行月份:', row.month);
                console.log('月份维度模式 - 比较月份:', comparisonMonths);
            }

            // 1. 计算同期比较（使用产品销售分析表的逻辑）
            const samePeriodComparison = calculateSamePeriodComparison(comparisonMonths, [], comparisonWhereCondition, comparisonParams);
            console.log('同期比较查询:', samePeriodComparison);

            // 2. 计算环比比较（使用产品销售分析表的逻辑）
            const sequentialComparison = calculateSequentialComparison(comparisonMonths, [], comparisonWhereCondition, comparisonParams);
            console.log('环比比较查询:', sequentialComparison);

            // 执行同期查询
            db.get(samePeriodComparison.currentQuery, samePeriodComparison.currentParams, (err, samePeriodCurrentResult) => {
                if (err) {
                    console.error('同期当期查询错误:', err);
                    samePeriodCurrentResult = { current: 0 };
                }

                db.get(samePeriodComparison.samePeriodQuery, samePeriodComparison.samePeriodParams, (err, samePeriodResult) => {
                    if (err) {
                        console.error('同期数据查询错误:', err);
                        samePeriodResult = { previous: 0 };
                    }

                    console.log('同期当期结果:', samePeriodCurrentResult);
                    console.log('同期去年结果:', samePeriodResult);

                    // 执行环比查询
                    db.get(sequentialComparison.currentQuery, sequentialComparison.currentParams, (err, sequentialCurrentResult) => {
                        if (err) {
                            console.error('环比当期查询错误:', err);
                            sequentialCurrentResult = { current: 0 };
                        }

                        db.get(sequentialComparison.previousQuery, sequentialComparison.previousParams, (err, sequentialResult) => {
                            if (err) {
                                console.error('环比数据查询错误:', err);
                                sequentialResult = { previous: 0 };
                            }

                            console.log('环比当期结果:', sequentialCurrentResult);
                            console.log('环比上期结果:', sequentialResult);

                            const samePeriodCurrent = samePeriodCurrentResult?.current || 0;
                            const samePeriodPrevious = samePeriodResult?.previous || 0;
                            const sequentialCurrent = sequentialCurrentResult?.current || 0;
                            const sequentialPrevious = sequentialResult?.previous || 0;

                            const samePeriodGrowth = calculateGrowthRate(samePeriodCurrent, samePeriodPrevious);
                            const sequentialGrowth = calculateGrowthRate(sequentialCurrent, sequentialPrevious);

                            console.log('计算结果 - 同期当期:', samePeriodCurrent, '同期去年:', samePeriodPrevious, '同期增长:', samePeriodGrowth);
                            console.log('计算结果 - 环比当期:', sequentialCurrent, '环比上期:', sequentialPrevious, '环比增长:', sequentialGrowth);

                            processedRows[index] = {
                                ...row,
                                lastYearSales: samePeriodPrevious,
                                lastPeriodSales: sequentialPrevious,
                                samePeriodGrowth: samePeriodGrowth,
                                sequentialGrowth: sequentialGrowth,
                                // 添加调试信息到返回数据中
                                debugInfo: {
                                    samePeriodCurrent,
                                    samePeriodPrevious,
                                    sequentialCurrent,
                                    sequentialPrevious,
                                    samePeriodQuery: samePeriodComparison.samePeriodQuery,
                                    sequentialQuery: sequentialComparison.previousQuery
                                }
                            };

                            completedRows++;

                            // 当所有行都处理完成后，返回结果
                            if (completedRows === totalRows) {
                                console.log('=== 所有行处理完成，返回结果 ===');
                                console.log('处理后的数据样本:', processedRows.slice(0, 2));
                                res.json({
                                    success: true,
                                    data: processedRows
                                });
                            }
                        });
                    });
                });
            });
        });
    });
}

// 医院销量占比柱状图数据API
app.post('/api/hospital-sales-chart', authenticateToken, (req, res) => {
    const { products, months, quarters, terminals, reps, regionProductLines } = req.body;

    console.log('=== 医院销量占比柱状图API调用 ===');
    console.log('用户:', req.user.username, '角色:', req.user.role);
    console.log('筛选条件:', { products, months, quarters, terminals, reps, regionProductLines });

    // 构建基础WHERE条件
    let whereCondition = '1=1';
    let params = [];

    // 添加筛选条件
    if (products && products.length > 0) {
        const productConditions = products.map(() => '产品 = ?').join(' OR ');
        whereCondition += ` AND (${productConditions})`;
        params.push(...products);
    }

    if (months && months.length > 0) {
        const monthConditions = months.map(() => '月份 = ?').join(' OR ');
        whereCondition += ` AND (${monthConditions})`;
        params.push(...months);
    }

    if (quarters && quarters.length > 0) {
        const quarterConditions = quarters.map(() => '季度 = ?').join(' OR ');
        whereCondition += ` AND (${quarterConditions})`;
        params.push(...quarters);
    }

    if (terminals && terminals.length > 0) {
        const terminalConditions = terminals.map(() => '医院 = ?').join(' OR ');
        whereCondition += ` AND (${terminalConditions})`;
        params.push(...terminals);
    }

    if (reps && reps.length > 0) {
        const repConditions = reps.map(() => '新REP = ?').join(' OR ');
        whereCondition += ` AND (${repConditions})`;
        params.push(...reps);
    }

    if (regionProductLines && regionProductLines.length > 0) {
        const regionProductLineConditions = regionProductLines.map(() => '地区产品线 = ?').join(' OR ');
        whereCondition += ` AND (${regionProductLineConditions})`;
        params.push(...regionProductLines);
    }

    // 查询医院销量数据
    const hospitalQuery = `
        SELECT
            医院 as hospital,
            SUM(CAST(销售金额折算后 AS REAL)) as totalSales
        FROM salestable
        WHERE ${whereCondition} AND 医院 IS NOT NULL AND 医院 != ''
        GROUP BY 医院
        ORDER BY totalSales DESC
    `;

    console.log('医院销量查询SQL:', hospitalQuery);
    console.log('查询参数:', params);

    db.all(hospitalQuery, params, (err, results) => {
        if (err) {
            console.error('查询医院销量数据失败:', err);
            return res.status(500).json({ success: false, error: '查询医院销量数据失败' });
        }

        console.log('医院销量查询结果数量:', results.length);

        if (results.length === 0) {
            return res.json({
                success: true,
                data: {
                    chartData: [],
                    shouldDisplay: false,
                    totalSales: 0,
                    hospitalCount: 0
                }
            });
        }

        // 计算总销量
        const totalSales = results.reduce((sum, item) => sum + item.totalSales, 0);
        console.log('总销量:', totalSales);

        // 计算累计销量占比，找出占总销量80%的医院
        let cumulativeSales = 0;
        const top80PercentHospitals = [];

        for (const hospital of results) {
            cumulativeSales += hospital.totalSales;
            const cumulativePercentage = (cumulativeSales / totalSales) * 100;

            top80PercentHospitals.push({
                hospital: hospital.hospital,
                totalSales: hospital.totalSales,
                percentage: ((hospital.totalSales / totalSales) * 100).toFixed(1),
                cumulativePercentage: cumulativePercentage.toFixed(1)
            });

            // 当累计占比达到80%时停止
            if (cumulativePercentage >= 80) {
                break;
            }
        }

        console.log('占比80%的医院数量:', top80PercentHospitals.length);
        console.log('医院销量占比数据:', top80PercentHospitals.slice(0, 5)); // 只打印前5个

        res.json({
            success: true,
            data: {
                chartData: top80PercentHospitals,
                shouldDisplay: top80PercentHospitals.length > 0,
                totalSales: totalSales,
                hospitalCount: results.length,
                top80Count: top80PercentHospitals.length
            }
        });
    });
});

// 产品医院分布分析API
app.post('/api/product-hospital-analysis', authenticateToken, (req, res) => {
    const { productName, months, quarters, terminals, reps, regionProductLines } = req.body;

    console.log('=== 产品医院分布分析API调用 ===');
    console.log('产品名称:', productName);
    console.log('筛选条件:', { months, quarters, terminals, reps, regionProductLines });

    // 构建WHERE条件
    let whereCondition = '产品 = ?';
    let params = [productName];

    // 权限控制
    const isAdmin = req.user.role === 'admin' || req.user.username === 'admin';
    if (!isAdmin && req.user.repCode) {
        whereCondition += ' AND 新REP = ?';
        params.push(req.user.repCode);
    }

    // 添加其他筛选条件
    if (months && months.length > 0) {
        const placeholders = months.map(() => '?').join(',');
        whereCondition += ` AND 月份 IN (${placeholders})`;
        params.push(...months);
    }

    if (quarters && quarters.length > 0) {
        const placeholders = quarters.map(() => '?').join(',');
        whereCondition += ` AND 季度 IN (${placeholders})`;
        params.push(...quarters);
    }

    if (terminals && terminals.length > 0) {
        const placeholders = terminals.map(() => '?').join(',');
        whereCondition += ` AND 医院 IN (${placeholders})`;
        params.push(...terminals);
    }

    if (reps && reps.length > 0) {
        const placeholders = reps.map(() => '?').join(',');
        whereCondition += ` AND 新REP IN (${placeholders})`;
        params.push(...reps);
    }

    if (regionProductLines && regionProductLines.length > 0) {
        const placeholders = regionProductLines.map(() => '?').join(',');
        whereCondition += ` AND 地区产品线 IN (${placeholders})`;
        params.push(...regionProductLines);
    }

    // 查询医院数据
    const hospitalQuery = `
        SELECT
            医院 as name,
            SUM(CAST(销售金额折算后 AS REAL)) as actual,
            SUM(CAST(指标金额 AS REAL)) as target,
            COUNT(*) as recordCount
        FROM salestable
        WHERE ${whereCondition} AND 医院 IS NOT NULL AND 医院 != ''
        GROUP BY 医院
        ORDER BY actual DESC
    `;

    console.log('执行医院查询:', hospitalQuery);
    console.log('查询参数:', params);

    db.all(hospitalQuery, params, (err, hospitalRows) => {
        if (err) {
            console.error('查询医院数据错误:', err);
            return res.status(500).json({
                success: false,
                error: '查询医院数据失败'
            });
        }

        console.log('医院数据行数:', hospitalRows.length);
        console.log('医院数据样本:', hospitalRows.slice(0, 3));

        // 为每个医院计算去年同期数据
        let completedHospitals = 0;
        const hospitalsWithComparison = [];

        if (hospitalRows.length === 0) {
            return res.json({
                success: true,
                data: {
                    hospitals: []
                }
            });
        }

        hospitalRows.forEach((row) => {
            // 查询去年同期数据
            const lastYearQuery = `
                SELECT SUM(CAST(销售金额折算后 AS REAL)) as lastYearSales
                FROM salestable
                WHERE ${whereCondition.replace(/月份 IN \([^)]+\)/g, '月份 IN (' + (months || []).map(m => `'${parseInt(m) - 100}'`).join(',') + ')')}
                AND 医院 = ?
            `;

            const lastYearParams = [...params, row.name];

            db.get(lastYearQuery, lastYearParams, (err, lastYearData) => {
                if (err) {
                    console.error(`获取医院 ${row.name} 去年数据错误:`, err);
                }

                hospitalsWithComparison.push({
                    name: row.name || '未知医院',
                    actual: row.actual || 0,
                    target: row.target || 0,
                    lastYearSales: lastYearData?.lastYearSales || 0,
                    recordCount: row.recordCount || 0
                });

                completedHospitals++;

                if (completedHospitals === hospitalRows.length) {
                    res.json({
                        success: true,
                        data: {
                            hospitals: hospitalsWithComparison
                        }
                    });
                }
            });
        });
    });
});

// 医院产品分布分析API
app.post('/api/hospital-product-analysis', authenticateToken, (req, res) => {
    const { hospitalName, months, quarters, terminals, reps, regionProductLines } = req.body;

    console.log('=== 医院产品分布分析API调用 ===');
    console.log('医院名称:', hospitalName);
    console.log('筛选条件:', { months, quarters, terminals, reps, regionProductLines });

    // 构建WHERE条件
    let whereCondition = '医院 = ?';
    let params = [hospitalName];

    // 权限控制
    const isAdmin = req.user.role === 'admin' || req.user.username === 'admin';
    if (!isAdmin && req.user.repCode) {
        whereCondition += ' AND 新REP = ?';
        params.push(req.user.repCode);
    }

    // 添加其他筛选条件
    if (months && months.length > 0) {
        const placeholders = months.map(() => '?').join(',');
        whereCondition += ` AND 月份 IN (${placeholders})`;
        params.push(...months);
    }

    if (quarters && quarters.length > 0) {
        const placeholders = quarters.map(() => '?').join(',');
        whereCondition += ` AND 季度 IN (${placeholders})`;
        params.push(...quarters);
    }

    if (reps && reps.length > 0) {
        const placeholders = reps.map(() => '?').join(',');
        whereCondition += ` AND 新REP IN (${placeholders})`;
        params.push(...reps);
    }

    if (regionProductLines && regionProductLines.length > 0) {
        const placeholders = regionProductLines.map(() => '?').join(',');
        whereCondition += ` AND 地区产品线 IN (${placeholders})`;
        params.push(...regionProductLines);
    }

    // 查询产品数据
    const productQuery = `
        SELECT
            产品 as name,
            SUM(CAST(销售金额折算后 AS REAL)) as actual,
            SUM(CAST(指标金额 AS REAL)) as target,
            COUNT(*) as recordCount
        FROM salestable
        WHERE ${whereCondition} AND 产品 IS NOT NULL AND 产品 != ''
        GROUP BY 产品
        ORDER BY actual DESC
    `;

    console.log('执行产品查询:', productQuery);
    console.log('查询参数:', params);

    db.all(productQuery, params, (err, productRows) => {
        if (err) {
            console.error('查询产品数据错误:', err);
            return res.status(500).json({
                success: false,
                error: '查询产品数据失败'
            });
        }

        console.log('产品数据行数:', productRows.length);
        console.log('产品数据样本:', productRows.slice(0, 3));

        // 为每个产品计算去年同期数据
        let completedProducts = 0;
        const productsWithComparison = [];

        if (productRows.length === 0) {
            return res.json({
                success: true,
                data: {
                    products: []
                }
            });
        }

        productRows.forEach((row) => {
            // 查询去年同期数据
            const lastYearQuery = `
                SELECT SUM(CAST(销售金额折算后 AS REAL)) as lastYearSales
                FROM salestable
                WHERE ${whereCondition.replace(/月份 IN \([^)]+\)/g, '月份 IN (' + (months || []).map(m => `'${parseInt(m) - 100}'`).join(',') + ')')}
                AND 产品 = ?
            `;

            const lastYearParams = [...params, row.name];

            db.get(lastYearQuery, lastYearParams, (err, lastYearData) => {
                if (err) {
                    console.error(`获取产品 ${row.name} 去年数据错误:`, err);
                }

                productsWithComparison.push({
                    name: row.name || '未知产品',
                    actual: row.actual || 0,
                    target: row.target || 0,
                    lastYearSales: lastYearData?.lastYearSales || 0,
                    recordCount: row.recordCount || 0
                });

                completedProducts++;

                if (completedProducts === productRows.length) {
                    res.json({
                        success: true,
                        data: {
                            products: productsWithComparison
                        }
                    });
                }
            });
        });
    });
});

// 获取季度对应的月份
function getQuarterMonths(quarter) {
    // 季度格式可能是 "2024Q1" 或 "Q1"
    const quarterStr = String(quarter);
    let qNum;

    if (quarterStr.includes('Q')) {
        qNum = parseInt(quarterStr.substring(quarterStr.indexOf('Q') + 1));
    } else {
        qNum = parseInt(quarterStr);
    }

    const currentYear = new Date().getFullYear();

    switch (qNum) {
        case 1:
            return [`${currentYear}01`, `${currentYear}02`, `${currentYear}03`];
        case 2:
            return [`${currentYear}04`, `${currentYear}05`, `${currentYear}06`];
        case 3:
            return [`${currentYear}07`, `${currentYear}08`, `${currentYear}09`];
        case 4:
            return [`${currentYear}10`, `${currentYear}11`, `${currentYear}12`];
        default:
            return [];
    }
}

// 医院同期对比柱状图数据API
app.post('/api/hospital-comparison-chart', authenticateToken, (req, res) => {
    const { products, months, quarters, terminals, reps, regionProductLines } = req.body;

    console.log('=== 医院同期对比柱状图API调用 ===');
    console.log('用户:', req.user.username, '角色:', req.user.role);
    console.log('筛选条件:', { products, months, quarters, terminals, reps, regionProductLines });

    // 构建基础WHERE条件
    let whereCondition = '1=1';
    let params = [];

    // 权限控制
    const isAdmin = req.user.role === 'admin' || req.user.username === 'admin';
    if (!isAdmin && req.user.repCode) {
        whereCondition += ' AND 新REP = ?';
        params.push(req.user.repCode);
    }

    // 添加筛选条件
    if (products && products.length > 0) {
        const productConditions = products.map(() => '产品 = ?').join(' OR ');
        whereCondition += ` AND (${productConditions})`;
        params.push(...products);
    }

    if (months && months.length > 0) {
        const placeholders = months.map(() => '?').join(',');
        whereCondition += ` AND 月份 IN (${placeholders})`;
        params.push(...months);
    }

    if (quarters && quarters.length > 0) {
        const quarterConditions = quarters.map(quarter => {
            const quarterMonths = getQuarterMonths(quarter);
            return `月份 IN (${quarterMonths.map(() => '?').join(',')})`;
        }).join(' OR ');
        whereCondition += ` AND (${quarterConditions})`;
        quarters.forEach(quarter => {
            params.push(...getQuarterMonths(quarter));
        });
    }

    if (terminals && terminals.length > 0) {
        const terminalConditions = terminals.map(() => '终端 = ?').join(' OR ');
        whereCondition += ` AND (${terminalConditions})`;
        params.push(...terminals);
    }

    if (reps && reps.length > 0) {
        const repConditions = reps.map(() => '新REP = ?').join(' OR ');
        whereCondition += ` AND (${repConditions})`;
        params.push(...reps);
    }

    if (regionProductLines && regionProductLines.length > 0) {
        const regionConditions = regionProductLines.map(() => '大区产品线 = ?').join(' OR ');
        whereCondition += ` AND (${regionConditions})`;
        params.push(...regionProductLines);
    }

    // 获取当前年份和去年年份
    const currentYear = new Date().getFullYear();
    const lastYear = currentYear - 1;

    console.log('当前年份:', currentYear, '去年年份:', lastYear);

    // 查询医院同期对比数据
    const hospitalQuery = `
        SELECT
            医院 as hospital,
            SUM(CASE WHEN 年份 = ? THEN CAST(销售金额折算后 AS REAL) ELSE 0 END) as currentYearSales,
            SUM(CASE WHEN 年份 = ? THEN CAST(销售金额折算后 AS REAL) ELSE 0 END) as lastYearSales
        FROM salestable
        WHERE ${whereCondition}
        AND 医院 IS NOT NULL
        AND 医院 != ''
        AND (年份 = ? OR 年份 = ?)
        GROUP BY 医院
        HAVING (currentYearSales > 0 OR lastYearSales > 0)
        ORDER BY currentYearSales DESC
        LIMIT 15
    `;

    const queryParams = [currentYear, lastYear, ...params, currentYear, lastYear];

    console.log('医院同期对比查询SQL:', hospitalQuery);
    console.log('查询参数:', queryParams);

    db.all(hospitalQuery, queryParams, (err, results) => {
        if (err) {
            console.error('查询医院同期对比数据失败:', err);
            return res.status(500).json({ success: false, error: '查询医院同期对比数据失败' });
        }

        console.log('医院同期对比查询结果数量:', results.length);

        if (results.length === 0) {
            return res.json({
                success: true,
                data: {
                    chartData: [],
                    shouldDisplay: false
                }
            });
        }

        console.log('医院同期对比数据:', results.slice(0, 5)); // 只打印前5个

        res.json({
            success: true,
            data: {
                chartData: results,
                shouldDisplay: results.length > 0
            }
        });
    });
});

// 医院环比增长柱状图数据API
app.post('/api/hospital-mom-chart', authenticateToken, (req, res) => {
    const { products, months, quarters, terminals, reps, regionProductLines } = req.body;

    console.log('=== 医院环比增长柱状图API调用 ===');
    console.log('用户:', req.user.username, '角色:', req.user.role);
    console.log('筛选条件:', { products, months, quarters, terminals, reps, regionProductLines });

    // 构建基础WHERE条件
    let whereCondition = '1=1';
    let params = [];

    // 权限控制
    const isAdmin = req.user.role === 'admin' || req.user.username === 'admin';
    if (!isAdmin && req.user.repCode) {
        whereCondition += ' AND 新REP = ?';
        params.push(req.user.repCode);
    }

    // 添加筛选条件
    if (products && products.length > 0) {
        const productConditions = products.map(() => '产品 = ?').join(' OR ');
        whereCondition += ` AND (${productConditions})`;
        params.push(...products);
    }

    if (months && months.length > 0) {
        const placeholders = months.map(() => '?').join(',');
        whereCondition += ` AND 月份 IN (${placeholders})`;
        params.push(...months);
    }

    if (quarters && quarters.length > 0) {
        const quarterConditions = quarters.map(quarter => {
            const quarterMonths = getQuarterMonths(quarter);
            return `月份 IN (${quarterMonths.map(() => '?').join(',')})`;
        }).join(' OR ');
        whereCondition += ` AND (${quarterConditions})`;
        quarters.forEach(quarter => {
            params.push(...getQuarterMonths(quarter));
        });
    }

    if (terminals && terminals.length > 0) {
        const terminalConditions = terminals.map(() => '终端 = ?').join(' OR ');
        whereCondition += ` AND (${terminalConditions})`;
        params.push(...terminals);
    }

    if (reps && reps.length > 0) {
        const repConditions = reps.map(() => '新REP = ?').join(' OR ');
        whereCondition += ` AND (${repConditions})`;
        params.push(...reps);
    }

    if (regionProductLines && regionProductLines.length > 0) {
        const regionConditions = regionProductLines.map(() => '大区产品线 = ?').join(' OR ');
        whereCondition += ` AND (${regionConditions})`;
        params.push(...regionProductLines);
    }

    // 获取当前月份和上个月份
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript月份从0开始
    const currentYear = currentDate.getFullYear();

    let previousMonth, previousYear;
    if (currentMonth === 1) {
        previousMonth = 12;
        previousYear = currentYear - 1;
    } else {
        previousMonth = currentMonth - 1;
        previousYear = currentYear;
    }

    // 格式化月份为YYYYMM格式
    const currentMonthStr = `${currentYear}${currentMonth.toString().padStart(2, '0')}`;
    const previousMonthStr = `${previousYear}${previousMonth.toString().padStart(2, '0')}`;

    console.log('当前月份:', currentMonthStr, '上个月份:', previousMonthStr);

    // 查询医院环比增长数据
    const hospitalQuery = `
        SELECT
            医院 as hospital,
            SUM(CASE WHEN 年份 = ? AND 月份 = ? THEN CAST(销售金额折算后 AS REAL) ELSE 0 END) as currentMonthSales,
            SUM(CASE WHEN 年份 = ? AND 月份 = ? THEN CAST(销售金额折算后 AS REAL) ELSE 0 END) as previousMonthSales
        FROM salestable
        WHERE ${whereCondition}
        AND 医院 IS NOT NULL
        AND 医院 != ''
        AND ((年份 = ? AND 月份 = ?) OR (年份 = ? AND 月份 = ?))
        GROUP BY 医院
        HAVING (currentMonthSales > 0 OR previousMonthSales > 0)
        ORDER BY currentMonthSales DESC
        LIMIT 15
    `;

    const queryParams = [currentYear, currentMonth, previousYear, previousMonth, ...params, currentYear, currentMonth, previousYear, previousMonth];

    console.log('医院环比增长查询SQL:', hospitalQuery);
    console.log('查询参数:', queryParams);

    db.all(hospitalQuery, queryParams, (err, results) => {
        if (err) {
            console.error('查询医院环比增长数据失败:', err);
            return res.status(500).json({ success: false, error: '查询医院环比增长数据失败' });
        }

        console.log('医院环比增长查询结果数量:', results.length);

        if (results.length === 0) {
            return res.json({
                success: true,
                data: {
                    chartData: [],
                    shouldDisplay: false
                }
            });
        }

        console.log('医院环比增长数据:', results.slice(0, 5)); // 只打印前5个

        res.json({
            success: true,
            data: {
                chartData: results,
                shouldDisplay: results.length > 0
            }
        });
    });
});

// 医院销量统计API（用于80%销量医院列表）
app.post('/api/hospital-sales-stats', authenticateToken, (req, res) => {
    const { months, quarters, terminals, reps, regionProductLines, products, percentage = 80 } = req.body;

    console.log('=== 医院销量统计API调用 ===');
    console.log('筛选条件:', { months, quarters, terminals, reps, regionProductLines, products, percentage });

    // 构建WHERE条件
    let whereCondition = '1=1';
    let params = [];

    // 权限控制
    const isAdmin = req.user.role === 'admin' || req.user.username === 'admin';
    if (!isAdmin && req.user.repCode) {
        whereCondition += ' AND 新REP = ?';
        params.push(req.user.repCode);
    }

    // 添加筛选条件
    if (months && months.length > 0) {
        const placeholders = months.map(() => '?').join(',');
        whereCondition += ` AND 月份 IN (${placeholders})`;
        params.push(...months);
    }

    if (quarters && quarters.length > 0) {
        const placeholders = quarters.map(() => '?').join(',');
        whereCondition += ` AND 季度 IN (${placeholders})`;
        params.push(...quarters);
    }

    if (terminals && terminals.length > 0) {
        const placeholders = terminals.map(() => '?').join(',');
        whereCondition += ` AND 医院 IN (${placeholders})`;
        params.push(...terminals);
    }

    if (reps && reps.length > 0) {
        const placeholders = reps.map(() => '?').join(',');
        whereCondition += ` AND 新REP IN (${placeholders})`;
        params.push(...reps);
    }

    if (products && products.length > 0) {
        const placeholders = products.map(() => '?').join(',');
        whereCondition += ` AND 产品 IN (${placeholders})`;
        params.push(...products);
    }

    if (regionProductLines && regionProductLines.length > 0) {
        const placeholders = regionProductLines.map(() => '?').join(',');
        whereCondition += ` AND 地区产品线 IN (${placeholders})`;
        params.push(...regionProductLines);
    }

    // 查询所有医院的销售数据
    const hospitalQuery = `
        SELECT
            医院 as name,
            SUM(CAST(销售金额折算后 AS REAL)) as actual,
            SUM(CAST(指标金额 AS REAL)) as target,
            COUNT(*) as recordCount
        FROM salestable
        WHERE ${whereCondition} AND 医院 IS NOT NULL AND 医院 != ''
        GROUP BY 医院
        ORDER BY actual DESC
    `;

    console.log('执行医院统计查询:', hospitalQuery);
    console.log('查询参数:', params);

    db.all(hospitalQuery, params, (err, hospitalRows) => {
        if (err) {
            console.error('查询医院统计数据错误:', err);
            return res.status(500).json({
                success: false,
                error: '查询医院统计数据失败'
            });
        }

        console.log('医院统计数据行数:', hospitalRows.length);

        if (hospitalRows.length === 0) {
            return res.json({
                success: true,
                data: {
                    hospitals: [],
                    totalSales: 0,
                    threshold: 0
                }
            });
        }

        // 计算总销售额
        const totalSales = hospitalRows.reduce((sum, hospital) => sum + (hospital.actual || 0), 0);
        const threshold = totalSales * (percentage / 100);

        // 按销售额降序排序并筛选出指定百分比的医院
        hospitalRows.sort((a, b) => (b.actual || 0) - (a.actual || 0));

        let cumulativeSales = 0;
        const topHospitals = [];

        for (let i = 0; i < hospitalRows.length; i++) {
            const hospital = hospitalRows[i];
            if (cumulativeSales < threshold) {
                cumulativeSales += hospital.actual || 0;
                topHospitals.push({
                    ...hospital,
                    rank: i + 1,
                    percentage: ((hospital.actual || 0) / totalSales * 100).toFixed(2),
                    cumulativePercentage: (cumulativeSales / totalSales * 100).toFixed(2)
                });
            } else {
                break;
            }
        }

        console.log(`筛选出前${percentage}%医院数量:`, topHospitals.length);
        console.log('医院统计样本:', topHospitals.slice(0, 3));

        res.json({
            success: true,
            data: {
                hospitals: topHospitals,
                totalSales: totalSales,
                threshold: threshold,
                percentage: percentage,
                totalHospitalCount: hospitalRows.length,
                selectedHospitalCount: topHospitals.length
            }
        });
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器运行在端口 ${PORT}`);
});
